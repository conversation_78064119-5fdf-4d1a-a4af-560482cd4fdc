POST /api/v1/sams/configuration/portal/getConfig HTTP/1.1
app-version: 5.0.107
auth-token: 
device-id: a5dd8aycj5lhsgyyzardnmgjzbg4n1mos4ar
device-name: iPhone 15 Plus
device-os-version: 12
device-type: android
language: CN
latitude: 
Local-Latitude: 0.0
Local-Longitude: 0.0
longitude: 
n: p4y9yi9cyumfukdow2etrpcfl79vzn1h
p: 1656120205
rcs: 5
spv: 2.0
st: 91010b065af6c9d7be7625253db5cc45
sy: 0
sny: c
system-language: CN
t: 1750575338714
tpg: 1
User-Agent: okhttp/4.8.1
zoneType: 1
Content-Type: application/json
Accept: */*
Host: api-sams.walmartmobile.cn
Accept-Encoding: gzip, deflate, br
Connection: keep-alive
Content-Length: 16

{"keyId":"info"}

HTTP/1.1 200
Date: Sun, 22 Jun 2025 06:55:38 GMT
Content-Type: application/json;charset=UTF-8
Transfer-Encoding: chunked
Connection: keep-alive
Access-Control-Allow-Origin: *
Set-Cookie: auth-token=; Max-Age=7200; Expires=Sun, 22-Jun-2025 08:55:38 GMT; Domain=qq.com; Path=/
srd: MTBfM18xNDhfOF8yMTVfNQ==
sbt: 1750575338884
siv: %8LpHj&20Kz@g1MF
ssk: cXs47lTAJEvEF0/P16ljxSLUb5EL2CA/dIcN328AxW5Eq+aIBCE+iFBqZMRGOrst
Access-Control-Allow-Credentials: true
Access-Control-Allow-Methods: POST,GET,OPTIONS
Access-Control-Expose-Headers: X-forwared-port, X-forwarded-host
Vary: Origin,Access-Control-Request-Method,Access-Control-Request-Headers
X-Content-Type-Options: nosniff
vary: Origin
X-Kong-Upstream-Latency: 10
X-Kong-Proxy-Latency: 0
Via: kong/2.5.1

1bf4
{"data":{"jsonConfigData":{ "widgetConfig" : { "widgetDistance": "200", "widgetEndTime": "3" }, "enterpriseProcurement": { "H5Link": "http://wsh.walmart2019.com/contact-sm.html" }, "receiveCoupon":{ "isShow":1, "ids":[ { "3209":"qsk_MxsxvGmiwKopcwIz9byVtFCKKC6Hzr0PszubUc4" } ] }, "saleActivityNotify":{ "isShow":1, "ids":[ { "666":"fOOTZsIY6au6s89oY9PB4p36wYnam_6mgMRn0rOkrOw" } ] }, "bindMemberCard":{ "isShow":1, "ids":[ { "3800":"xlWquaIAvWQnBwmRUZnYpd_KyH3b0J4qW2jtrMlKoHc" } ] }, "renewalNormalMember":{ "isShow":1, "ids":[ { "4117":"MaIa1B8wf1YYBKVWrFk5iLPnYcS9nQNonmzm34td8UQ" } ] }, "renewalSuperMember":{ "isShow":1, "ids":[ { "4117":"MaIa1B8wf1YYBKVWrFk5iLPnYcS9nQNonmzm34td8UQ" }, { "4184":"8X-0RjCHONR7WNzwxpcedhjL7S0NE-y58W9GYMje5W8" } ] }, "buyCardSuccess":{ "isShow":1, "ids":[ { "2380":"WqYyhFN16zjk48C1fNfxmMpOpeC-p6DladNLQH48_yI" }, { "666":"fOOTZsIY6au6s89oY9PB4ra6hqET68lYjkiED1PhbJs" }, { "4184":"8X-0RjCHONR7WNzwxpcedhjL7S0NE-y58W9GYMje5W8" } ] }, "upgradeCardSuccess":{ "isShow":1, "ids":[ { "2380":"WqYyhFN16zjk48C1fNfxmG2bmCTcpRY0VnTWyqPARD8" }, { "4184":"8X-0RjCHONR7WNzwxpcedhjL7S0NE-y58W9GYMje5W8" } ] }, "inviteSuccess":{ "isShow":1, "ids":[ { "3801":"Kf-_HYdW1Hy5yI-8qemtTpluC4KSUj96G8cq1TIOLkw" }, { "4537":"3Hv4709--HIJIYraDB7kg5OC448xgytt1rBt3pX7lmM" } ] }, "inviteAndActived":{ "isShow":1, "ids":[ { "3330":"yGgak4TjGsooVYuqqnxGKZL7jxUEgVXaCfxYW8Ahnvc" } ] }, "activateWishcardSuccess": { "isShow": 1, "ids": [ { "3801": "Kf-_HYdW1Hy5yI-8qemtTpluC4KSUj96G8cq1TIOLkw" } ] }, "buyWishcardSuccess": { "isShow": 1, "ids": [ { "3327": "qZQc26_rzjHCTfLVm1OdehObnoDKUAuEO5gr6skkbW0" }, { "3801": "Kf-_HYdW1Hy5yI-8qemtTpluC4KSUj96G8cq1TIOLkw" } ] }, "wishcardActived": { "isShow": 1, "ids": [ { "3327": "qZQc26_rzjHCTfLVm1OdehObnoDKUAuEO5gr6skkbW0" }, { "3330": "yGgak4TjGsooVYuqqnxGKcRecfPIYTt54PQ4YtaAecc" }, { "3800": "xlWquaIAvWQnBwmRUZnYpd_KyH3b0J4qW2jtrMlKoHc" } ] }, "launchCoupon": { "isShow": 1, "ids": [ { "3209": "qsk_MxsxvGmiwKopcwIz9byVtFCKKC6Hzr0PszubUc4" }, { "3118": "30wtmHmw9YRgMv4eRKgQH0zebQ_fWhG89df2HplBrzE" }, { "4184": "8X-0RjCHONR7WNzwxpcedhy8YD2ef3OriBQ54KcKuVc" } ] }, "orderPay": { "isShow": 1, "ids": [ { "1368": "e7a7k-brtZOW3nq9xwa0p-hurdLztYM5zbm81sx8xqw" }, { "666": "fOOTZsIY6au6s89oY9PB4ra6hqET68lYjkiED1PhbJs" }, { "4184": "8X-0RjCHONR7WNzwxpcedgzm0ZyG4O-4S1ZcixJ1aTs" } ] }, "eventNotification": { "isShow": 1, "ids": [ { "666": "fOOTZsIY6au6s89oY9PB4ra6hqET68lYjkiED1PhbJs" } ] }, "experienceCardReceived": { "isShow": 1, "ids": [ { "3330": "yGgak4TjGsooVYuqqnxGKZL7jxUEgVXaCfxYW8Ahnvc" }, { "3327": "qZQc26_rzjHCTfLVm1OdehObnoDKUAuEO5gr6skkbW0" }, { "21611": "xK3_wnQ8ITKL8WK5jFC3RTYZbu6b7BdL7-1lUgsAKaI" } ] }, "samsOperatingSubject": { "key1": "运营主体", "value1": "沃尔玛(中国)投资有限公司", "key2": "注册运营地址", "value2": "深圳市福田区香蜜湖街道竹园社区农林路69号印力中心4层T2-04-01（二号楼4层）、二号楼7层及三号楼1-12层", "key3": "内容生产类别", "value3": "山姆会员商店菜谱视频、山姆会员商店及商品介绍宣传视频等", "key4": "统一社会信用代码", "value4": "914403007109368585", "key5": "有效联系方式", "value5": "0755-23973113" }, "samsOperatingSubjectEn": { "key1": "Operating entity", "value1": "Walmart (China) Investment Co., Ltd.", "key2": "Registered operation address", "value2": "7/F, Bldg. 2, 1-12/F, Bldg. 3, T2-04-01 (4/F, Bldg. 2) 4/F, SCPG Center, 69 Nonglin Rd., Zhuyuan Comm., Xiangmihu St., Futian Dist. Shenzhen", "key3": "Content production category", "value3": "Sam&apos;s Club menu video, Sam&apos;s Club and goods introduction and promotional videos, etc.", "key4": "Unified social credit code", "value4": "914403007109368585", "key5": "Valid contact information", "value5": "0755-23973113" }, "commentAutoAuditRule": "请提交≥100字，≥3图的评价", "notMatchHkAddressText": "未匹配出预期地址，请手动输入详细地址", "notMatchHkAddressTextEn": "Address not found. Please enter your detailed address manually.", "openShareCardSwitch": true, "tastingCartConfig": { "appUrl" : "https://gz-cos-sam-yewu-online-01-1302115363.file.myqcloud.com/sams/tastingCart/app.png", "tipsTitleText": "评价将同步试吃车", "tipsTitleTextEn": "Reviews and tasting cart are synchronized.", "tipsSubtitleText": "了解如何写评价", "tipsSubtitleTextEn": "Learn how to write a review", "h5Url" : "https://gz-cos-sam-yewu-online-01-1302115363.file.myqcloud.com/sams/tastingCart/h5.png", "navbarUrl": "https://gz-cos-sam-yewu-online-01-1302115363.file.myqcloud.com/sams/tastingCart/navbarUrl.jpg", "flowChartUrl" : "https://gz-cos-sam-yewu-online-01-1302115363.file.myqcloud.com/sams/tastingCart/flowChartUrl.svg", "sharebarUrl" : "https://gz-cos-sam-yewu-online-01-1302115363.file.myqcloud.com/sams/tastingCart/sharebarUrl.jpg", "navbarLink": "https://m-sams.walmartmobile.cn/common/help-center/129", "flowChartLink" : "https://m-sams.walmartmobile.cn/common/help-center/129" } },"stopServiceVO":{"startTime":"2023-03-22 02:00:00","endTime":"2023-03-22 04:15:00","startRealTime":"1679421600000","endRealTime":"1679429700000","isStop":false},"wxLoginConfig":{"isOpen":true,"fixCloseVersion":[]},"isOpenWxMembershipFaceImg":true,"orderChannelMap":{"10":"SCAFE","13":"SSC","14":"SMC","15":"CPCSM","82":"CPCSMB"},"samsDiscoveryDefaultFrameUrl":"https://sam-web-admin-online-1302115363.file.myqcloud.com/discovery_home_page_cos","loginVideoUrl":"https://sam-material-online-1302115363.file.myqcloud.com/persist/3e89d264-b317-4241-a9df-4292c90871a7/1818/video/1faa0b52f224103f844d7e12d0686fc6.mp4","hippyList":[{"name":"main","iosLink":"https://gz-cos-sam-yewu-online-01-1302115363.cos.ap-guangzhou.myqcloud.com/download/hippy/v3/95/main.ios.zip","androidLink":"https://gz-cos-sam-yewu-online-01-1302115363.cos.ap-guangzhou.myqcloud.com/download/hippy/v3/95/main.android.zip","harmonyLink":"https://gz-cos-sam-yewu-online-01-1302115363.cos.ap-guangzhou.myqcloud.com/download/hippy/v3/95/main.ohos.zip","version":"95"}],"enabledConfig":{"improveInfoTaskEnabled":true,"washCarBenefitEnabled":true,"allowModifyUserInfoFlag":false,"addCartExpEnabled":true,"showGrayEnabled":false,"wxGateReqChanEnabled":true,"arrivalNoticeEnabled":true,"gwsv3Enabled":true,"inboxBottomOrderTextEnabled":true},"discoverHotEnabled":false,"currentSysTimestamp":"1750575338882","nextRefreshTimestamp":"0","limitRequestInfo":{"isOpen":true,"requestThreshold":"1000","limitText":"当前购物火爆，请稍后再试","limitTextEn":"Shopping is hot at present. Please try again later"},"enterprisePurchaseApplink":"sams://decoration_preview?pageContentId=1530649697700727062","amapTypes":"01|02|03|04|05|06|07|08|09|10|110000|110100|110106|110204|110205|110206|110207|120200|120201|120202|120203|120300|120301|120302|120303|120304||13|14|15|16|17|200000|200100|200200|200300|200301|200302|200303|200304|200400|970000|990000|991000|991001|991400|991401","amapStyleId":"973268f25b7f45ea65fc66879fed7807"},"code":"Success","msg":"","errorMsg":"","traceId":"e66deacc2d37e415","requestId":"4ac36a78c7d740c28a9b2eeb93e7a0c2.246.17505753388800979","rt":0,"clientIp":null,"success":true}
0

