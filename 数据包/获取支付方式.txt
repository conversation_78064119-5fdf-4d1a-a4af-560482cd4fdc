POST /api/v1/sams/configuration/paymentStore4C/queryList2 HTTP/1.1
sy: 0
system-language: CN
Local-Latitude: 0.0
device-os-version: 12
User-Agent: okhttp/4.8.1
spv: 2.0
device-name: HONOR_BVL-AN20
longitude: 113.727975
tpg: 1
t: 1746868471845
device-id: 98fd40ddd4d4c304ae0d1de210001b71941a
Accept-Encoding: gzip
Host: api-sams.walmartmobile.cn
Content-Type: application/json;charset=utf-8
latitude: 22.979352
p: 1656120205
n: a798f2f3887244bb980a91717c32c96a
device-type: android
app-version: 5.0.107
Connection: Keep-Alive
treq-id: 239d409588ba4f399745b55dcb1580a7.273.17468686350518583
Local-Longitude: 0.0
st: 02708a414f79f39ac27d1937d3644670
language: CN
rcs: 1
sny: c
Cookie: auth-token=740d926b981716f4d32fbd79178a293000579a136dd71577c951cddcb8d97103e61386009cc75cc38f9d1a3f88fb7dfa266b927ff8178dd4
auth-token: 740d926b981716f4d32fbd79178a293000579a136dd71577c951cddcb8d97103e61386009cc75cc38f9d1a3f88fb7dfa266b927ff8178dd4
zoneType: 1
Content-Length: 160

{"list":[{"clientType":"APP","orderType":"H","spuIdList":[255887993],"storeId":5390},{"clientType":"APP","orderType":"A","spuIdList":[1340023],"storeId":6570}]}

HTTP/1.1 200
Date: Sat, 10 May 2025 09:14:32 GMT
Content-Type: application/json;charset=UTF-8
Transfer-Encoding: chunked
Connection: keep-alive
Access-Control-Allow-Origin: *
Set-Cookie: auth-token=740d926b981716f4d32fbd79178a293000579a136dd71577c951cddcb8d97103e61386009cc75cc38f9d1a3f88fb7dfa266b927ff8178dd4; Max-Age=7200; Expires=Sat, 10-May-2025 11:14:32 GMT; Domain=qq.com; Path=/
srd: MTBfM18xNDhfOF8yMTVfNQ==
sbt: 1746868472467
siv: %8LpHj&20Kz@g1MF
ssk: uK4LEuvx2rOqr1cQp50aZcKz7qfl22GYRTLLeaXCwJkLJ0FTEA4PrznU0BCKVKUL
Access-Control-Allow-Credentials: true
Access-Control-Allow-Methods: POST,GET,OPTIONS
Access-Control-Expose-Headers: X-forwared-port, X-forwarded-host
Vary: Origin,Access-Control-Request-Method,Access-Control-Request-Headers
X-Content-Type-Options: nosniff
vary: Origin
X-Kong-Upstream-Latency: 96
X-Kong-Proxy-Latency: 0
Via: kong/2.5.1

3a7
{"data":{"paymentStoreInfoList":[{"saasId":"1818","subSaasId":"2088041266518571","storeId":"5390","channel":"alipay","sortIndex":1,"subTitle":"","isAutoSelectLast":1,"isFold":0,"subChannel":null,"tipContent":null},{"saasId":"1818","subSaasId":"802440348991058","storeId":"5390","channel":"china_unionpay","sortIndex":2,"subTitle":"","isAutoSelectLast":1,"isFold":0,"subChannel":null,"tipContent":null},{"saasId":"1818","subSaasId":"20200920","storeId":"5390","channel":"sam_coupon","sortIndex":3,"subTitle":"","isAutoSelectLast":1,"isFold":0,"subChannel":null,"tipContent":null},{"saasId":"1818","subSaasId":"1486659732","storeId":"5390","channel":"wechat","sortIndex":4,"subTitle":"","isAutoSelectLast":0,"isFold":0,"subChannel":null,"tipContent":null}]},"code":"Success","msg":"","errorMsg":"","traceId":"df20517c1a34709d","requestId":"as|239d409588ba4f399745b55dcb1580a7.273.17468686350518583","rt":0,"clientIp":null,"success":true}
0

