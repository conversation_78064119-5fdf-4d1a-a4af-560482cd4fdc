POST /api/v1/sams/sams-user/membership/gift_card/create_invitation HTTP/1.1
User-Agent: okhttp/4.8.1
App-Version: 5.0.107
Spv: 2.0
Sy: 0
Rcs: 1
Sny: c
Language: CN
P: 1656120205
ZoneType: 1
Tpg: 1
n: f7kcuwws0u5ec49uxq8f02en6kkcik1s
t: 1752994448445
st: c2032a25888fb0b869e31c1ee4c72310
Device-Type: android
Device-Name: iPhone 15 Plus
device-id: fsbph6a5a4lvk2q1me6v1c2s780tcwzeocnu
Auth-Token: 740d926b981716f4c55d65c891d082d09c31082b448eebce64b3d763547070f562090006ef52f9b581a1c23129bdffad344dd2d849b7d8cc
Content-Type: application/json
Accept: */*
Host: api-sams.walmartmobile.cn
Accept-Encoding: gzip, deflate, br
Connection: keep-alive
Content-Length: 2

{}

HTTP/1.1 200
Date: Sun, 20 Jul 2025 06:54:08 GMT
Content-Type: application/json;charset=UTF-8
Transfer-Encoding: chunked
Connection: keep-alive
Access-Control-Allow-Origin: *
Set-Cookie: auth-token=740d926b981716f4c55d65c891d082d09c31082b448eebce64b3d763547070f562090006ef52f9b581a1c23129bdffad344dd2d849b7d8cc; Max-Age=7200; Expires=Sun, 20 Jul 2025 08:54:08 GMT; Domain=qq.com; Path=/
srd: MTBfM18xNDhfOF8yMTVfNQ==
sbt: 1752994448601
siv: %8LpHj&20Kz@g1MF
ssk: PvsoIqH/VPTIi5V9gjI6Gqcy+KCi2DPq6THUc3c0a4k2xANc1e+n2LfEOXlGbHqB
Access-Control-Allow-Credentials: true
Access-Control-Allow-Methods: POST,GET,OPTIONS
Access-Control-Expose-Headers: X-forwared-port, X-forwarded-host
Vary: Origin,Access-Control-Request-Method,Access-Control-Request-Headers
X-Content-Type-Options: nosniff
vary: Origin
X-Kong-Upstream-Latency: 53
X-Kong-Proxy-Latency: 0
Via: kong/2.5.1

5e6
{"data":{"lastName":"蒋","firstName":"璐","encryptionInfo":"x+Jmc2U9qql1fmKDRLsFEanIsK1ClxfDsFvdyvmt3Ajv1s0oHQ9hnmP10FoKzwfgE27PsfxsqW8ySq0r+rqAYs9TSvxE9Mr7Vxy6VZJRsnU=","memType":1,"h5FriendCardUrl":"https://m-sams.walmartmobile.cn/h5/family-card","qrCode":"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAOQAAADkAQAAAACgLLUgAAAC4klEQVR42r2ZUXLkQAhDuQH3vyU3YNFTj7/2V5kkVbbVU4tpIdRsVVXPTu1slX71Kf28Twjt3a45/NYI6r5HU7MHRNHqu9Ofwrvbu7gVdd/pNFqlqBSLPrfobv8CFSL4bsv4H6Dahcv+aFFrE8QDXX17FEFFrf9+Pk5mUJFbbB8WrQi4TtCqBmIo+7CkYVxxgn5ECKJNVE0wo6el6ladTQVRMqKXV1mLbuOM3Bdqgqj+aT25kI5wulXRIWZcxFCoLRU5UEFJUNl6XebQo9pYzKSiSsUlg9veJFpk36SbITalR+yvKOrmpEgW+XSfWr4QRNfMU1poF+tsaMEk0VGRqT+LBqzhO9qFLMqLe4nEzDlQYtCWGKqe0fYhCkzOACZqK5LovA4F20X3NdUlbUmUYKA5kiLxZJUUvYKopUOVpk1AO4mLEgiiv41AyCTfIrtIsBVFh0aM16NX4Ptmns8Nonp3Uu7kS9bGhiyKwgAX9VjHtBPuWpNEoRvGD+Zhtf3UxiyEulGsDQL11vNdJlHygas11b4qsz1IodiOUXk/C+jWVU/rYii5KFsttmHtcheVCaK776VV31AdRV2f32KoyqnJPYcmrV5CU36CaNnGt40BjqhsiCSwOVTShbdceN5Utzo13jqHvqOoT8drAd13PI6iHEptsinxQcJJUUXRF1Yj1v1bO596Z1D7D48hLOF0LPXJniDKfMUdggMjqajxRCSI6qlCgHVwkMHH2onmUHqUK5px0xuG4DqTqApbhSxXwimReQD730m03Iv1CH4zSHSJTRL17GN9aOLg1Lac9aaIKRTGz2d/7DcRs5ogWmYYU1IxwSpjFU+iNGMkhdd/ORkPFLPovq0gE/umTH6QQ98U0fFwIfrjstGcFMrwgXzgfPoblr5uFUPL8/fxJnjSYnmdSqPr8xqDPR9Zx2fGLPpOqTRmZorjWXkUtXyT/aVhkRUsdhLFyZvq9CnLC/8vUEH0H5FYjf3SNqA0AAAAAElFTkSuQmCC"},"code":"Success","msg":"","errorMsg":"","traceId":"d6ab48bc59cca5e1","requestId":"as|c2b9e165256d4fdab15ec790eaa35a8b.1539.17529944485666791","rt":0,"success":true}
0

