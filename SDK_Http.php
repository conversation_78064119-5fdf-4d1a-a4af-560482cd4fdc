<?php
namespace app\common\model;

class SDK_Http
{
    public function http_curl($url, $paras = array()) {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

        // 支持自定义HTTP头
        $Header = !empty($paras['Header']) ? $paras['Header'] : array(
            "Accept:*/*",
            "Accept-Encoding:gzip,deflate,sdch",
            "Accept-Language:zh-CN,zh;q=0.8",
            "Connection:close",
            "User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.6045.160 Safari/537.36"
        );
        curl_setopt($ch, CURLOPT_HTTPHEADER, $Header);

        // 连接超时
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, !empty($paras['ctime']) ? $paras['ctime'] : 60);

        // 响应超时
        if (!empty($paras['rtime'])) {
            curl_setopt($ch, CURLOPT_TIMEOUT, $paras['rtime']);
        }

        // 请求方法处理
        $method = !empty($paras['method']) ? strtoupper($paras['method']) : '';
        if($method) {
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
            // 对于POST, PUT, PATCH等可能包含数据的请求方法，设置相应的选项
            if(in_array($method, ['POST', 'PUT', 'PATCH'])) {
                if($method == 'POST') {
                    curl_setopt($ch, CURLOPT_POST, TRUE);
                }
                
                // 支持JSON格式请求体
                if(!empty($paras['json'])) {
                    $json_data = is_array($paras['json']) ? json_encode($paras['json']) : $paras['json'];
                    curl_setopt($ch, CURLOPT_POSTFIELDS, $json_data);
                    // 添加JSON内容类型头
                    $json_header = ["Content-Type: application/json"];
                    curl_setopt($ch, CURLOPT_HTTPHEADER, array_merge($Header, $json_header));
                }
            }
        }

        // 处理POST请求
        if (!empty($paras['post'])) {
            if(empty($method)){
                curl_setopt($ch, CURLOPT_POST, TRUE);
            }
            
            // 支持数组或字符串形式的POST数据
            if(is_array($paras['post']) && !empty($paras['form_data'])) {
                // multipart/form-data格式提交
                curl_setopt($ch, CURLOPT_POSTFIELDS, $paras['post']);
            } else {
                // 普通POST处理
                curl_setopt($ch, CURLOPT_POSTFIELDS, $paras['post']);
            }
        }

        // 支持上传文件
        if(!empty($paras['files'])) {
            if(is_array($paras['files'])) {
                $post_data = !empty($paras['post']) && is_array($paras['post']) ? $paras['post'] : [];
                foreach($paras['files'] as $key => $file_path) {
                    if(file_exists($file_path)) {
                        $post_data[$key] = new CURLFile($file_path);
                    }
                }
                curl_setopt($ch, CURLOPT_POSTFIELDS, $post_data);
                curl_setopt($ch, CURLOPT_POST, TRUE);
            }
        }

        // 代理设置
        if (!empty($paras['proxy'])) {
            $proxy = function_exists('is_ip_with_port') && is_ip_with_port($paras['proxy']) ? $paras['proxy'] : ($paras['proxy']);
            $parts = explode('@', $proxy);
            if (count($parts) === 2) {
                $ip_address = $parts[0];
                $proxy_auth = $parts[1];
                curl_setopt($ch, CURLOPT_PROXY, $ip_address);
                curl_setopt($ch, CURLOPT_PROXYUSERPWD, $proxy_auth);
            } else {
                curl_setopt($ch, CURLOPT_PROXY, $proxy);
            }
            
            // 支持代理类型设置
            if(!empty($paras['proxy_type'])) {
                $proxy_type = strtolower($paras['proxy_type']);
                if($proxy_type == 'socks5') {
                    curl_setopt($ch, CURLOPT_PROXYTYPE, CURLPROXY_SOCKS5);
                } elseif($proxy_type == 'socks4') {
                    curl_setopt($ch, CURLOPT_PROXYTYPE, CURLPROXY_SOCKS4);
                } elseif($proxy_type == 'http') {
                    curl_setopt($ch, CURLOPT_PROXYTYPE, CURLPROXY_HTTP);
                }
            }
        }

        // 本地调试
        curl_setopt($ch, CURLOPT_PROXY, '127.0.0.1:2024');
//        curl_setopt($ch, CURLOPT_PROXY, '**************:18111');
//        curl_setopt($ch, CURLOPT_PROXYUSERPWD, 'x383:x383g');
        if (!empty($paras['debug'])) {
            curl_setopt($ch, CURLOPT_PROXY, '127.0.0.1:2024');
        }

        if (!empty($paras['ip'])) {
            //ip是127.0.0.1:8000@user:user 自动拆分
            $parts = explode('@', trim($paras['ip']));
            if (count($parts) === 2) {
                $ip_address = $parts[0];
                $proxy_auth = $parts[1];
                curl_setopt($ch, CURLOPT_PROXY, $ip_address);
                curl_setopt($ch, CURLOPT_PROXYUSERPWD, $proxy_auth);
            } else {
                curl_setopt($ch, CURLOPT_PROXY, $parts[0]);
            }
        }

        if (!empty($paras['proxy_auth'])) {
            curl_setopt($ch, CURLOPT_PROXYUSERPWD, trim($paras['proxy_auth']));
        }

        // 设置HTTP基本认证
        if(!empty($paras['auth'])) {
            if(is_array($paras['auth']) && count($paras['auth']) == 2) {
                curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
                curl_setopt($ch, CURLOPT_USERPWD, $paras['auth'][0] . ':' . $paras['auth'][1]);
            } elseif(is_string($paras['auth'])) {
                curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
                curl_setopt($ch, CURLOPT_USERPWD, $paras['auth']);
            }
        }

        if (!empty($paras['header'])) {
            curl_setopt($ch, CURLOPT_HEADER, true);
        }

        if (!empty($paras['cookie'])) {
            curl_setopt($ch, CURLOPT_COOKIE, $paras['cookie']);
        }
        if (!empty($paras['cookieFile'])) {
            curl_setopt($ch, CURLOPT_COOKIEJAR, $paras['cookieFile']); // 将收到的 Cookie 保存到文件
            curl_setopt($ch, CURLOPT_COOKIEFILE, $paras['cookieFile']); // 使用保存的 Cookie 文件发送请求
        }

        // 自动重定向
        if(!empty($paras['auto'])){
            curl_setopt($ch, CURLOPT_MAXREDIRS, !empty($paras['max_redirs']) ? $paras['max_redirs'] : 10);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
            // 允许自定义是否保留重定向前的cookies
            if(!empty($paras['redir_with_cookie'])) {
                curl_setopt($ch, CURLOPT_AUTOREFERER, 1);
            }
        }

        if (!empty($paras['refer'])) {
            curl_setopt($ch, CURLOPT_REFERER, $paras['refer']);
        }

        if (!empty($paras['ua'])) {
            curl_setopt($ch, CURLOPT_USERAGENT, $paras['ua']);
        } else {
            curl_setopt($ch, CURLOPT_USERAGENT, "Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/65.0.3325.181 Safari/537.36");
        }

        if (!empty($paras['nobody'])) {
            curl_setopt($ch, CURLOPT_NOBODY, 1);
        }

        // 支持DNS预解析
        if(!empty($paras['dns_cache'])) {
            curl_setopt($ch, CURLOPT_DNS_USE_GLOBAL_CACHE, true);
            if(!empty($paras['dns_cache_timeout'])) {
                curl_setopt($ch, CURLOPT_DNS_CACHE_TIMEOUT, $paras['dns_cache_timeout']);
            }
        }

        // 设置接口操作
        if(!empty($paras['interface'])) {
            curl_setopt($ch, CURLOPT_INTERFACE, $paras['interface']);
        }

        // 支持设置SSL版本
        if(!empty($paras['ssl_version'])) {
            curl_setopt($ch, CURLOPT_SSLVERSION, $paras['ssl_version']);
        }

        // 支持自定义证书
        if(!empty($paras['ssl_cert'])) {
            curl_setopt($ch, CURLOPT_SSLCERT, $paras['ssl_cert']);
            if(!empty($paras['ssl_cert_type'])) {
                curl_setopt($ch, CURLOPT_SSLCERTTYPE, $paras['ssl_cert_type']);
            }
        }
        
        if(!empty($paras['ssl_key'])) {
            curl_setopt($ch, CURLOPT_SSLKEY, $paras['ssl_key']);
        }

        curl_setopt($ch, CURLOPT_ENCODING, "gzip");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);

        // 支持获取响应的HTTP状态码
        if (!empty($paras['return_status'])) {
            $result = curl_exec($ch);
            $status_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $ret = [
                'status' => $status_code,
                'body' => $result,
            ];
            
            // 添加详细信息
            if(!empty($paras['return_info'])) {
                $ret['info'] = curl_getinfo($ch);
            }
            
            curl_close($ch);
            return $ret;
        }

        // 获取Cookie
        if (!empty($paras['GetCookie'])) {
            curl_setopt($ch, CURLOPT_HEADER, 1);
            $result = curl_exec($ch);
            
            // 检查请求是否成功
            if($result === false) {
                $error = curl_error($ch);
                curl_close($ch);
                return ['error' => $error];
            }
            
            // 获取 Set-Cookie 的键值对
            preg_match_all('/Set-Cookie: ([^=]+)=([^;]+);/mi', $result, $matches, PREG_SET_ORDER);

            $cookies = [];
            foreach ($matches as $match) {
                $cookies[$match[1]] = $match[2];
            }
            $final_url = curl_getinfo($ch, CURLINFO_EFFECTIVE_URL) ?? '';

            $headerSize = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
            $header = substr($result, 0, $headerSize);
            $header = explode("\r\n\r\n", trim($header));
            $header = array_pop($header);
            $header = explode("\r\n", $header);
            array_shift($header);
            $header_assoc = [];
            foreach ($header as $header_line) {
                $kv = explode(': ', $header_line, 2);
                if(count($kv) == 2) {
                    $header_assoc[strtolower($kv[0])] = $kv[1];
                }
            }
            $body = substr($result, $headerSize);
            $ret = [
                "Cookie" => $cookies,
                "body" => $body,
                "code" => $header_assoc,
                "final_url" => $final_url,
                "status" => curl_getinfo($ch, CURLINFO_HTTP_CODE),
            ];
            curl_close($ch);
            return $ret;
        }

        // 执行请求
        $ret = curl_exec($ch);
        
        // 处理错误
        if($ret === false && !empty($paras['return_error'])) {
            $error = curl_error($ch);
            $error_code = curl_errno($ch);
            curl_close($ch);
            return [
                'error' => $error,
                'error_code' => $error_code
            ];
        }

        if (!empty($paras['loadurl'])) {
            $Headers = curl_getinfo($ch);
            $ret = $Headers['redirect_url'];
        }

        curl_close($ch);
        return $ret;
    }
}