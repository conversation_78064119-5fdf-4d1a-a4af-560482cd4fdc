<?php
require_once 'SamClub.php';
$token = '740d926b981716f4e10d985ca9ea62082a13734794149c1b806d40636dd3439b7e7cfa8e7c9fd32081a1c23129bdffad9f6ccc15a6666e58';
$sam = new app\common\model\SamClub();
//$result = $sam->query_navigation($token);
//print_r($result);
//
//
//// 使用"肉蛋果蔬"的groupingId 35145
//$result = $sam->query_children('35145', $token);
//print_r($result);

//{"latitude":27.975731,"longitude":113.046308}
//$result = $sam->get_recommend_store_list('27.995731', '113.046308', $token);
//print_r($result);

// 获取指定极速达门店的特定分类商品
$categoryGoods = $sam->get_category_goods('6114', $token, '', '', [
    'frontCategoryIds'=>[227216,275053,276052,225240,226211,227218,226210,225244,228238,225243,228236,283020,282016,281032,225242,227217,226212,228237,227219,225241,228239],
    'deliveryType' => [1],
]);
print_r($categoryGoods);
//$orderData = [
//    "receiverInfo" => [
//        "phone" => "18888888888",
//        "backupPhone" => "",
//        "address" => "石碶街道鄞县大道西段888号石碶地铁站E口雅渡新村11幢3单元605",
//        "doorNumber" => "1011",
//        "latitude" => "29.824926",
//        "longitude" => "121.505859"
//    ],
//    "userId" =>1818144585036
//];
//$sam->login_success('');
// $detail = $sam->get_store_list($token, '', '');
// print_r($detail);
//$messageList = $sam->get_system_message_list($token );
//print_r($messageList);
