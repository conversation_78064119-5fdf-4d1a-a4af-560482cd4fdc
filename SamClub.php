<?php
namespace app\common\model;

if (!function_exists('public_path')) {
    function public_path(): string
    {
        return __DIR__ . '/public/';
    }
}

class SamClub
{
    private $http;
    /**
     * API域名常量
     */
    private $domain = 'https://api-sams.walmartmobile.cn';
    public function __construct()
    {
        require_once __DIR__ . '/SDK_Http.php';
        $this->http = new \app\common\model\SDK_Http();

    }
    public function http_curl($url, $options = [])
    {
        return $this->http->http_curl($url, $options);
    }

    /**
     * 加密请求头
     *
     * 该方法用于生成加密的请求头数组，其中包括了设备信息、位置信息、应用版本信息等
     * 主要进行了设备ID的处理、密钥的获取、以及签名的生成等工作
     *
     * @param string $body 请求体内容，用于签名生成
     * @param string $token 用户令牌，用于生成设备ID和签名
     * @param string $device_id 设备ID，如果为空会根据token生成或使用时间戳生成
     * @param string $device_type 设备类型，默认为'android'
     * @param string $lon 经度，用于请求头中的位置信息
     * @param string $lat 纬度，用于请求头中的位置信息
     *
     * @return array 加密后的请求头数组
     */
    private function header_encrypt(string $body, string $token, string $device_id = '', string $device_type = 'android', string $lon = '0', string $lat = '0'): array
    {
        // 处理设备ID，如果为空则根据token或时间戳生成
        if(empty($device_id)){
            if(!empty($token)){
                $device_id = md5($token);
            }else{
                $device_id = md5(time());
            }
        }elseif ($device_id == 'xiaxia'){
            $device_id = md5(rand(1000000000,9999999999).'xiaxia') ;
        }
        $device_name = 'iPhone 15 Plus';

        // 判断文件夹是否存在
        if (!is_dir(public_path() .'sams_key')) {
            mkdir(public_path() .'sams_key', 0777, true);
        }

        // 获取加密密钥，首先尝试从本地文件读取，如果为空则从远程API获取并保存到本地
        if (!file_exists(public_path() .'sams_key/key.txt')) {
            $key = file_get_contents("https://panda.jucai.wang/sams_key/key.txt");
        }else{
            $key = file_get_contents(public_path() .'sams_key/key.txt');
        }
        if(empty($key)){
            $key = file_get_contents("https://panda.jucai.wang/sams_key/key.txt");
            file_put_contents(public_path().'sams_key/key.txt',$key);
        }
        //$key = 'Mqnh2%hx^t80$TASkHj3%897E^z&&)2F';

        $app_Version = "5.0.105";

        // 获取当前Unix时间戳，用于签名生成
        $t = $this->getUnixTimestamp();

        // 生成签名
        $n = md5($t . 'JkkxbDBPSTA4SFlrbzslJA==');
        $st = md5("{$n}{$t}{$key}{$body}{$token}" );
        $st_key = 'B6@N7#M8$Q9%W1^E';
        $st = md5("{$st}{$device_id}{$device_type}{$app_Version}{$st_key}" );

        // 构建请求头数组
        return ["Accept-Language: zh-cn", "Connection: Keep-Alive", "Content-Type: application/json", "User-Agent: okhttp/4.8.1", "language: CN", "p: 1656120205", "rcs: 1", "sny: c", "spv: 2.0", "sy: 0", "tpg: 1", "zoneType: 1", "lon: $lon", "lat: $lat", "n: $n", "t: $t", "st: $st", "app-version: $app_Version", "auth-token: $token", "device-id: $device_id", "device-name: $device_name", "device-os-version: 12", "device-type: $device_type"];
    }

    /**
     * 获取当前Unix时间戳（毫秒级）
     *
     * 该方法用于获取当前的时间戳，用于加密签名等场景
     *
     * @return float 当前Unix时间戳，单位为毫秒
     */
    private function getUnixTimestamp(): float
    {
        list($s1, $s2) = explode(' ', microtime());
        return (float)sprintf('%.0f', (floatval($s1) + floatval($s2)) * 1000);
    }

    /**
     * AES加密
     *
     * 该方法使用AES算法对数据进行加密，并返回加密后的数据
     * 主要用于对敏感数据进行加密传输或存储
     *
     * @param string $data 需要加密的数据
     * @param string $key 加密密钥，默认值为一个预设的密钥
     * @param string $iv 初始化向量，默认值为一个预设的向量
     *
     * @return string 加密后的数据，如果加密失败则返回错误信息
     */
    private function AES_Encrypt(string $data, string $key="&I1l0OI08HYko;%$", string $iv="%$&I080OIHYko1l;"): string
    {
        try {
            $cipher = "aes-128-cbc";
            // 确保密钥和初始化向量的长度符合要求
            $key = str_pad($key, 16, "\0");
            $iv = str_pad($iv, 16, "\0");

            $encryptedData = openssl_encrypt($data, $cipher, $key, OPENSSL_RAW_DATA, $iv);
            if ($encryptedData === false) {
                throw new Exception("Encryption failed.");
            }

            return base64_encode($encryptedData); // 编码为 base64
        } catch (Exception $e) {
            return "Error: " . $e->getMessage();
        }
    }


    /*
 * 发送验证码
 * @param string $phone 手机号
 * @param string $token token
 * @param string $device_id 设备id
 * @param string $ip 代理IP
 */
    public function send_code(string $phone, string $token = '', string $device_id = '', string $ip = '')
    {
        $validatePhoneNumber = $this->validatePhoneNumber($phone);
        $countryCode = $validatePhoneNumber['area_code'];
        $tongDunReportData = $this->generateTongDunReportData('web');
        $post_data = json_encode(['countryCode'=>$countryCode,'mobile' => $phone,'tongDunReportData'=>$tongDunReportData]);
        $header = $this->header_encrypt($post_data,$token,$device_id);
        $ajax_url = $this->domain . '/api/v1/sams/sams-user/authorization/send_code';
        $ret = $this->http_curl($ajax_url, ['method'=>'POST','post'=>$post_data,'Header'=>$header,'ip'=>$ip]);
        return $ret;
    }
    /*
     * 登录
     * @param string $phone 手机号
     * @param string $code 验证码
     * @param string $device_id 设备id
     * @param string $ip 代理IP
     */
    public function mobile(string $phone, string $code, string $device_id = '', string $ip = '')
    {
        $device_id  = $this->not_login_get_member_bind_device($phone, $code, $ip);
        $validatePhoneNumber = $this->validatePhoneNumber($phone);
        $countryCode = $validatePhoneNumber['area_code'];
        $tongDunReportData = $this->generateTongDunReportData('web');
        $post_data = json_encode(['countryCode'=>$countryCode,'mobile' => $phone, 'code' => $code,'tongDunReportData'=>$tongDunReportData]);
        $header = $this->header_encrypt($post_data,'',$device_id);
        $ajax_url = $this->domain . '/api/v1/sams/sams-user/authorization/mobile';
        $response = $this->http_curl($ajax_url, ['method'=>'POST','post'=>$post_data,'Header'=>$header,'ip'=>$ip]);
        $ret = json_decode($response,true);
        if ($ret !== null && isset($ret['success']) && $ret['success'] === true) {
            $token = $ret['data']['authToken'];
            $this->login_success($token,$device_id,$ip);
        }
        return $response;
    }


    /*
 * 登录成功后模拟请求
 * @param string $token token
 * @param string $device_id 设备id
 * @param string $ip 代理IP
 */
    public function login_success(string $token, string $device_id = '', string $ip = '')
    {
        $this->user_select($token,$device_id,$ip);
        $this->profile($token,$device_id,$ip);
        $this->member_card_info($token,$device_id,$ip);
        $this->get_config($token,$device_id,$ip);
    }


    /*
     * 用户选择
     * @param string $token token
     * @param string $device_id 设备id
     * @param string $ip 代理IP
     */
    public function user_select(string $token, string $device_id = '', string $ip = '')
    {
        $post_data = '';
        $header = $this->header_encrypt($post_data,$token,$device_id);
        $ajax_url = $this->domain . '/api/v1/sams/sams-user/user/tag/user_select';
        $ret = $this->http_curl($ajax_url, ['method'=>'GET','Header'=>$header,'ip'=>$ip]);
        return $ret;
    }

    /*
     * 用户资料
     * @param string $token token
     * @param string $device_id 设备id
     * @param string $ip 代理IP
     */
    public function profile(string $token, string $device_id = '', string $ip = '')
    {
        $post_data = '';
        $header = $this->header_encrypt($post_data,$token,$device_id);
        $ajax_url = $this->domain . '/api/v1/sams/sams-user/user/profile';
        $ret = $this->http_curl($ajax_url, ['method'=>'GET','Header'=>$header,'ip'=>$ip]);
        return $ret;
    }

    /*
     * 会员卡信息
     * @param string $token token
     * @param string $device_id 设备id
     * @param string $ip 代理IP
     */
    public function member_card_info(string $token, string $device_id = '', string $ip = '')
    {
        $post_data = '';
        $header = $this->header_encrypt($post_data,$token,$device_id);
        $ajax_url = $this->domain . '/api/v1/sams/sams-user/user/member_card_info';
        $ret = $this->http_curl($ajax_url, ['method'=>'GET','Header'=>$header,'ip'=>$ip]);
        return $ret;
    }


    /*
     * 检查登录
     * @param string $token token
     * @param string $device_id 设备id
     * @param string $ip 代理IP
     */
    public function check_login(string $token, string $device_id = '', string $ip = '')
    {
        $post_data = '{"channel":"app","userSign":"1"}';
        $header = $this->header_encrypt($post_data,$token,$device_id);
        $ajax_url = $this->domain . '/api/v1/sams/sams-user/agreement/user/check_login';
        $ret = $this->http_curl($ajax_url, ['method'=>'POST','post'=>$post_data,'Header'=>$header,'ip'=>$ip]);
        $ret = json_decode($ret,true);
        if ($ret !== null && isset($ret['success']) && $ret['success'] === true) {
            $loginStatus = true;
        }else{
            $loginStatus = false;
        }
        return $loginStatus;
    }

    /*
 * 个人中心信息
 * @param string $token token
 * @param string $device_id 设备id
 * @param string $ip 代理IP
 */
    public function personal_center_info(string $token, string $device_id = '', string $ip = '')
    {
        $post_data = '';
        $header = $this->header_encrypt($post_data,$token,$device_id);
        $ajax_url = $this->domain . '/api/v1/sams/sams-user/user/personal_center_info';
        $ret = $this->http_curl($ajax_url, ['method'=>'GET','Header'=>$header,'ip'=>$ip]);
        return $ret;
    }

    /*
     * 查看用户地址列表
     * @param string $token token
     * @param string $device_id 设备id
     * @param string $ip 代理IP
     */
    public function get_address_list(string $token, string $device_id = '', string $ip = '')
    {
        $post_data = '{}';
        $header = $this->header_encrypt($post_data, $token, $device_id);
        $ajax_url = $this->domain . '/api/v1/sams/sams-user/receiver_address/address_list?v=1.0';
        $ret = $this->http_curl($ajax_url, ['method'=>'POST','post'=>$post_data,'Header'=>$header,'ip'=>$ip]);
        return $ret;
    }

    /*
     * 获取附近门店
     * @param float $latitude 纬度
     * @param float $longitude 经度
     * @param string $token token
     * @param string $device_id 设备id
     * @param string $ip 代理IP
     */
    public function get_recommend_store_list(float $latitude, float $longitude, string $token, string $device_id = '', string $ip = '')
    {
        $post_data = json_encode(['latitude' => $latitude, 'longitude' => $longitude]);
        $header = $this->header_encrypt($post_data, $token, $device_id, 'android', (string)$longitude, (string)$latitude);
        $ajax_url = $this->domain . '/api/v1/sams/merchant/storeApi/getRecommendStoreListByLocation';
        $ret = $this->http_curl($ajax_url, ['method'=>'POST','post'=>$post_data,'Header'=>$header,'ip'=>$ip]);
        return $ret;
    }

    /*
     * 搜索商品
     * @param string $keyword 搜索关键词
     * @param string $token 用户令牌
     * @param string $device_id 设备ID
     * @param string $ip 代理IP
     * @param array $options 可选参数
     *   - pageNum: 页码，默认为1
     *   - pageSize: 每页显示数量，默认为20
     *   - sort: 排序方式，默认为0（综合排序）
     *   - storeInfoList: 门店信息列表
     *   - address: 地址信息，包含经纬度
     *   - userUid: 用户UID
     *   - uid: 指定UID
     *   - uidType: UID类型，默认为3
     *   - showCustomTag: 是否显示自定义标签，默认为true
     *   - overseasFilter: 是否过滤海外商品，默认为false
     *   - deliveryType: 配送类型，默认为空数组
     *   - filter: 筛选条件，默认为空数组
     */
    public function search_goods(string $keyword, string $token = '', string $device_id = '', string $ip = '', array $options = [])
    {
        // 设置默认参数
        $default_options = [
            'pageNum' => 1,
            'pageSize' => 20,
            'sort' => '0',
            'storeInfoList' => [],
            'address' => [],
            'userUid' => '',
            'uid' => '',
            'uidType' => 3,
            'showCustomTag' => true,
            'overseasFilter' => false,
            'deliveryType' => [],
            'filter' => []
        ];

        // 合并用户提供的选项与默认选项
        $options = array_merge($default_options, $options);

        // 处理默认门店信息
        if (empty($options['storeInfoList'])) {
            $options['storeInfoList'] = [
                ["storeId" => 6758, "storeType" => 256, "storeDeliveryAttr" => [5, 6, 9, 12, 13, 14]],
                ["storeId" => 6570, "storeType" => 2, "storeDeliveryAttr" => [3, 4, 7, 13]],
                ["storeId" => 5390, "storeType" => 4, "storeDeliveryAttr" => [3, 4]],
                ["storeId" => 9992, "storeType" => 8, "storeDeliveryAttr" => [1]]
            ];
        }

        // 处理默认地址信息
        if (empty($options['address'])) {
            $options['address'] = [
                "cityName" => "东莞市",
                "countryName" => "中国",
                "detailAddress" => "101",
                "districtName" => "东莞市",
                "provinceName" => "广东省",
                "longitude" => "113.727975",
                "latitude" => "22.979352"
            ];
        }

        // 从地址信息中获取经纬度
        $longitude = isset($options['address']['longitude']) ? $options['address']['longitude'] : '0';
        $latitude = isset($options['address']['latitude']) ? $options['address']['latitude'] : '0';

        // 确保地址信息的字段正确
        $addressVO = array_intersect_key($options['address'], array_flip([
            'cityName', 'countryName', 'detailAddress', 'districtName', 'provinceName'
        ]));

        // 如果没有指定用户UID，则生成一个随机UID
        if (empty($options['userUid'])) {
            $options['userUid'] = sprintf("%d", rand(1000000000000, 9999999999999));
        }

        // 如果没有指定UID，则生成一个随机UID
        if (empty($options['uid'])) {
            $options['uid'] = md5(uniqid(mt_rand(), true));
        }

        // 构建请求参数
        $request_data = [
            'userUid' => $options['userUid'],
            'pageNum' => $options['pageNum'],
            'pageSize' => $options['pageSize'],
            'keyword' => $keyword,
            'rewriteWord' => $keyword,
            'filter' => $options['filter'],
            'storeInfoVOList' => $options['storeInfoList'],
            'addressVO' => $addressVO,
            'uid' => $options['uid'],
            'uidType' => $options['uidType'],
            'sort' => $options['sort'],
            'isShowCustomTag' => $options['showCustomTag'],
            'overseasFilter' => $options['overseasFilter'],
            'deliveryType' => $options['deliveryType']
        ];

        // 转换请求参数为JSON格式
        $post_data = json_encode($request_data);

        // 生成加密请求头
        $header = $this->header_encrypt($post_data, $token, $device_id, 'android', $longitude, $latitude);

        // API请求地址
        $ajax_url = $this->domain . '/api/v1/sams/goods-portal/spu/search';

        // 发送请求
        $ret = $this->http_curl($ajax_url, ['method' => 'POST', 'post' => $post_data, 'Header' => $header, 'ip' => $ip]);

        return $ret;
    }

    /*
     * 添加商品到购物车
     * @param int $spuId 商品ID
     * @param int $storeId 门店ID
     * @param string $token 用户令牌
     * @param string $device_id 设备ID
     * @param string $ip 代理IP
     * @param array $options 可选参数
     *   - quantity: 添加数量，默认为1
     *   - isSelected: 是否选中，默认为true
     *   - isReplenishGoods: 是否为补货商品，默认为false
     *   - componentPath: 组件路径，默认为空
     *   - limitTag: 限制标签，默认为空
     *   - skuId: SKU ID，默认为-1
     *   - stockNotEnoughTag: 库存不足标签，默认为空
     *   - warrantyExtensionSpuIdList: 保修延期商品ID列表，默认为空数组
     *   - userUid: 用户UID
     *   - visitorId: 访客ID，默认为空
     */
    public function add_to_cart(int $spuId, int $storeId, string $token, string $device_id = '', string $ip = '', array $options = [])
    {
        // 设置默认参数
        $default_options = [
            'quantity' => 1,
            'isSelected' => true,
            'isReplenishGoods' => false,
            'componentPath' => '',
            'limitTag' => '',
            'skuId' => -1,
            'stockNotEnoughTag' => '',
            'warrantyExtensionSpuIdList' => [],
            'userUid' => '',
            'visitorId' => ''
        ];

        // 合并用户提供的选项与默认选项
        $options = array_merge($default_options, $options);

        // 如果没有指定用户UID，则生成一个随机UID
        if (empty($options['userUid'])) {
            $options['userUid'] = sprintf("%d", rand(1000000000000, 9999999999999));
        }

        // 构建请求参数
        $request_data = [
            'cartGoodsInfoList' => [
                [
                    'componentPath' => $options['componentPath'],
                    'increaseQuantity' => $options['quantity'],
                    'isReplenishGoods' => $options['isReplenishGoods'],
                    'isSelected' => $options['isSelected'],
                    'limitTag' => $options['limitTag'],
                    'skuId' => $options['skuId'],
                    'spuId' => $spuId,
                    'stockNotEnoughTag' => $options['stockNotEnoughTag'],
                    'storeId' => $storeId,
                    'warrantyExtensionSpuIdList' => $options['warrantyExtensionSpuIdList']
                ]
            ],
            'deviceType' => 'android',
            'standardSpuIds' => null,
            'uid' => $options['userUid'],
            'visitorId' => $options['visitorId']
        ];

        // 转换请求参数为JSON格式
        $post_data = json_encode($request_data);

        // 生成加密请求头
        $header = $this->header_encrypt($post_data, $token, $device_id);

        // API请求地址
        $ajax_url = $this->domain . '/api/v1/sams/trade/cart/addCartGoodsInfo';

        // 发送请求
        $ret = $this->http_curl($ajax_url, ['method' => 'POST', 'post' => $post_data, 'Header' => $header, 'ip' => $ip]);

        return $ret;
    }

    /*
     * 获取购物车商品数据
     * @param string $token 用户token
     * @param string $device_id 设备ID
     * @param string $ip 代理IP
     * @param array $options 可选参数
     * @return string 购物车数据（JSON字符串）
     */
    public function get_cart(string $token, string $device_id = '', string $ip = '', array $options = [])
    {
        // 设置默认参数
        $default_options = [
            'storeList' => [],
            'userUid' => '',
            'longitude' => '113.727975',
            'latitude' => '22.979352',
            'addressId' => 151542723
        ];

        // 合并用户提供的选项与默认选项
        $options = array_merge($default_options, $options);

        // 如果没有指定用户UID，则生成一个随机UID
        if (empty($options['userUid'])) {
            $options['userUid'] = sprintf("%d", rand(1000000000000, 9999999999999));
        }

        // 处理默认门店列表
        if (empty($options['storeList'])) {
            $options['storeList'] = [
                [
                    "storeId" => 6758,
                    "storeType" => 256,
                    "areaBlockId" => 42536,
                    "storeDeliveryTemplateId" => 626657353395258646,
                    "deliveryModeId" => 1006
                ],
                [
                    "storeId" => 6570,
                    "storeType" => 2,
                    "areaBlockId" => 2353958028014939670,
                    "storeDeliveryTemplateId" => 2581297327109190166,
                    "deliveryModeId" => 1019
                ],
                [
                    "storeId" => 5390,
                    "storeType" => 4,
                    "areaBlockId" => 2353958028014939670,
                    "storeDeliveryTemplateId" => 2384372897121755926,
                    "deliveryModeId" => 1009
                ],
                [
                    "storeId" => 9992,
                    "storeType" => 8,
                    "areaBlockId" => 42305,
                    "storeDeliveryTemplateId" => 1743371277198079766,
                    "deliveryModeId" => 1010
                ]
            ];
        }

        // 构建请求参数
        $request_data = [
            'storeList' => $options['storeList'],
            'uid' => $options['userUid'],
            'homePagelongitude' => $options['longitude'],
            'homePagelatitude' => $options['latitude'],
            'homePageAddressId' => $options['addressId']
        ];

        // 转换请求参数为JSON格式
        $post_data = json_encode($request_data);

        // 生成加密请求头
        $header = $this->header_encrypt($post_data, $token, $device_id, 'android', $options['longitude'], $options['latitude']);

        // API请求地址
        $ajax_url = $this->domain . '/api/v3/sams/trade/cart/getUserCart';

        // 发送请求
        $ret = $this->http_curl($ajax_url, ['method' => 'POST', 'post' => $post_data, 'Header' => $header, 'ip' => $ip]);

        return $ret;
    }

    /**
     * 统一请求方法
     *
     * @param string $url 请求URL
     * @param string $post_data 请求数据
     * @param string $token 用户token
     * @param string $device_id 设备ID
     * @param string $ip 代理IP
     * @return string 响应结果
     */
    private function request(string $url, string $post_data, string $token, string $device_id = '', string $ip = '')
    {
        $header = $this->header_encrypt($post_data, $token, $device_id);
        $ajax_url = $this->domain . $url;
        $ret = $this->http_curl($ajax_url, ['method'=>'POST','post'=>$post_data,'Header'=>$header,'ip'=>$ip]);
        return $ret;
    }

    /*
     * 获取报价单
     * @param string $token 用户令牌
     * @param string $device_id 设备ID
     * @param string $ip 代理IP
     * @param array $options 可选参数
     *   - storeList: 门店列表，默认为空数组
     *   - userUid: 用户UID
     *   - addressId: 地址ID
     *   - checkValidGoodsVOList: 商品列表，默认为空数组
     */
    public function get_pre_settle_info(string $token, string $device_id = '', string $ip = '', array $options = [])
    {
        // 设置默认参数
        $default_options = [
            'storeList' => [],
            'userUid' => '',
            'addressId' => 151542723,
            'checkValidGoodsVOList' => []
        ];

        // 合并用户提供的选项与默认选项
        $options = array_merge($default_options, $options);

        // 如果没有指定用户UID，则生成一个随机UID
        if (empty($options['userUid'])) {
            $options['userUid'] = sprintf("%d", rand(1000000000000, 9999999999999));
        }

        // 处理默认门店列表
        if (empty($options['storeList'])) {
            $options['storeList'] = [
                [
                    "storeId" => 6758,
                    "storeType" => 256,
                    "areaBlockId" => 42536,
                    "storeDeliveryTemplateId" => 626657353395258646,
                    "deliveryModeId" => 1006,
                    "storeDeliveryAttr" => [5, 6, 9, 12, 13, 14],
                    "storeDeliveryTemplateIdOriginal" => -1
                ],
                [
                    "storeId" => 6570,
                    "storeType" => 2,
                    "areaBlockId" => 2353958028014939670,
                    "storeDeliveryTemplateId" => 2581297327109190166,
                    "deliveryModeId" => 1019,
                    "storeDeliveryAttr" => [3, 4, 7, 13],
                    "storeDeliveryTemplateIdOriginal" => -1
                ],
                [
                    "storeId" => 5390,
                    "storeType" => 4,
                    "areaBlockId" => 2353958028014939670,
                    "storeDeliveryTemplateId" => 2384372897121755926,
                    "deliveryModeId" => 1009,
                    "storeDeliveryAttr" => [3, 4],
                    "storeDeliveryTemplateIdOriginal" => -1
                ],
                [
                    "storeId" => 9992,
                    "storeType" => 8,
                    "areaBlockId" => 42305,
                    "storeDeliveryTemplateId" => 1743371277198079766,
                    "deliveryModeId" => 1010,
                    "storeDeliveryAttr" => [1],
                    "storeDeliveryTemplateIdOriginal" => -1
                ]
            ];
        }

        // 构建请求参数
        $request_data = [
            'uid' => $options['userUid'],
            'storeList' => $options['storeList'],
            'checkValidGoodsVOList' => $options['checkValidGoodsVOList'],
            'addressId' => $options['addressId']
        ];

        // 转换请求参数为JSON格式
        $post_data = json_encode($request_data);

        // 生成加密请求头
        $header = $this->header_encrypt($post_data, $token, $device_id);

        // API请求地址
        $ajax_url = $this->domain . '/api/v3/sams/trade/cart/getPreSettleInfo';

        // 发送请求
        $ret = $this->http_curl($ajax_url, ['method' => 'POST', 'post' => $post_data, 'Header' => $header, 'ip' => $ip]);

        return $ret;
    }

    /*
     * 获取结算单
     * @param string $token 用户令牌
     * @param string $device_id 设备ID
     * @param string $ip 代理IP
     * @param array $options 可选参数
     *   - uid: 用户UID，默认随机生成
     *   - addressId: 地址ID，默认151542723
     *   - settleFloorDataList: 结算楼层数据，默认为空数组
     *   - gray: 是否灰度，默认true
     *   - showBuyToGo: 是否展示BuyToGo，默认true
     *   - buyToGoChangeAddress: BuyToGo是否可换地址，默认false
     */
    public function get_settle_info(string $token, string $device_id = '', string $ip = '', array $options = [])
    {
        // 设置默认参数
        $default_options = [
            'uid' => '',
            'addressId' => 151542723,
            'settleFloorDataList' => [],
            'gray' => true,
            'showBuyToGo' => true,
            'buyToGoChangeAddress' => false
        ];
        $options = array_merge($default_options, $options);
        if (empty($options['uid'])) {
            $options['uid'] = sprintf("%d", rand(1000000000000, 9999999999999));
        }
        $request_data = [
            'uid' => $options['uid'],
            'addressId' => $options['addressId'],
            'settleFloorDataList' => $options['settleFloorDataList'],
            'gray' => $options['gray'],
            'showBuyToGo' => $options['showBuyToGo'],
            'buyToGoChangeAddress' => $options['buyToGoChangeAddress']
        ];
        $post_data = json_encode($request_data);
        $header = $this->header_encrypt($post_data, $token, $device_id);
        $ajax_url = $this->domain . '/api/v3/sams/trade/settlement/getSettleInfo';
        $ret = $this->http_curl($ajax_url, ['method' => 'POST', 'post' => $post_data, 'Header' => $header, 'ip' => $ip]);
        return $ret;
    }

    /*
     * 获取配送时间
     * @param string $token 用户令牌
     * @param string $device_id 设备ID
     * @param string $ip 代理IP
     * @param array $options 可选参数
     *   - perDateList: 日期列表，默认为未来7天
     *   - storeDeliveryTemplateId: 配送模板ID，默认2384372897121755926
     *   - storeId: 门店ID，默认5390
     *   - floorId: 楼层ID，默认104
     *   - longitude: 经度，默认113.727975
     *   - latitude: 纬度，默认22.979352
     *   - isTransitStock: 是否为在途库存，默认false
     */
    public function get_capacity_data(string $token, string $device_id = '', string $ip = '', array $options = [])
    {
        // 默认未来7天日期
        $default_dates = [];
        for ($i = 0; $i < 7; $i++) {
            $default_dates[] = date('Y-m-d', strtotime("+{$i} day"));
        }
        // 设置默认参数
        $default_options = [
            'perDateList' => $default_dates,
            'storeDeliveryTemplateId' => 2384372897121755926,
            'storeId' => 5390,
            'floorId' => 104,
            'longitude' => 113.727975,
            'latitude' => 22.979352,
            'isTransitStock' => false
        ];
        $options = array_merge($default_options, $options);
        $request_data = [
            'perDateList' => $options['perDateList'],
            'storeDeliveryTemplateId' => $options['storeDeliveryTemplateId'],
            'storeId' => $options['storeId'],
            'floorId' => $options['floorId'],
            'longitude' => $options['longitude'],
            'latitude' => $options['latitude'],
            'isTransitStock' => $options['isTransitStock']
        ];
        $post_data = json_encode($request_data);
        $header = $this->header_encrypt($post_data, $token, $device_id, 'android', (string)$options['longitude'], (string)$options['latitude']);
        $ajax_url = $this->domain . '/api/v1/sams/delivery/portal/getCapacityData';
        $ret = $this->http_curl($ajax_url, ['method' => 'POST', 'post' => $post_data, 'Header' => $header, 'ip' => $ip]);
        return $ret;
    }

    /*
     * 检查商品
     * @param string $token 用户令牌
     * @param string $device_id 设备ID
     * @param string $ip 代理IP
     * @param array $options 可选参数
     *   - floorGoodsInfoList: 楼层商品信息列表，默认为空数组
     */
    public function check_goods_info(string $token, string $device_id = '', string $ip = '', array $options = [])
    {
        // 设置默认参数
        $default_options = [
            'floorGoodsInfoList' => []
        ];
        $options = array_merge($default_options, $options);
        $request_data = [
            'floorGoodsInfoList' => $options['floorGoodsInfoList']
        ];
        $post_data = json_encode($request_data);
        $header = $this->header_encrypt($post_data, $token, $device_id);
        $ajax_url = $this->domain . '/api/v3/sams/trade/settlement/checkGoodsInfo';
        $ret = $this->http_curl($ajax_url, ['method' => 'POST', 'post' => $post_data, 'Header' => $header, 'ip' => $ip]);
        return $ret;
    }

    /*
     * 获取一级分类
     * @param string $token 用户令牌
     * @param string $device_id 设备ID
     * @param string $ip 代理IP
     * @param array $options 可选参数
     *   - storeCategoryList: 商店分类列表，默认为空数组
     *   - isNew: 是否新版，默认为true
     */
    public function query_navigation(string $token, string $device_id = '', string $ip = '', array $options = [])
    {
        // 设置默认参数
        $default_options = [
            'storeCategoryList' => [],
            'isNew' => true
        ];
        $options = array_merge($default_options, $options);

        // 处理默认商店分类列表
        if (empty($options['storeCategoryList'])) {
            $options['storeCategoryList'] = [
                ["storeType" => 256, "storeId" => 6758, "storeDeliveryAttr" => [3, 4, 6, 14, 12, 5, 2, 9, 13]],
                ["storeType" => 2, "storeId" => 6505, "storeDeliveryAttr" => [7]],
                ["storeType" => 4, "storeId" => 6672, "storeDeliveryAttr" => [3, 4]],
                ["storeType" => 8, "storeId" => 9992, "storeDeliveryAttr" => [1]]
            ];
        }

        // 构建请求参数
        $request_data = [
            'storeCategoryList' => $options['storeCategoryList'],
            'isNew' => $options['isNew']
        ];

        // 转换请求参数为JSON格式
        $post_data = json_encode($request_data);

        // 生成加密请求头
        $header = $this->header_encrypt($post_data, $token, $device_id, 'android', '114.011671', '22.544368');

        // API请求地址
        $ajax_url = $this->domain . '/api/v1/sams/goods-portal/grouping/queryNavigation';

        // 发送请求
        $ret = $this->http_curl($ajax_url, ['method' => 'POST', 'post' => $post_data, 'Header' => $header, 'ip' => $ip]);

        return $ret;
    }

    /*
     * 获取二三级分类
     * @param string $groupingId 一级分类ID
     * @param string $token 用户令牌
     * @param string $device_id 设备ID
     * @param string $ip 代理IP
     * @param array $options 可选参数
     *   - storeCategoryList: 商店分类列表，默认为空数组
     *   - navigationId: 导航ID，默认为1
     *   - uid: 用户UID，默认随机生成
     */
    public function query_children(string $groupingId, string $token, string $device_id = '', string $ip = '', array $options = [])
    {
        // 设置默认参数
        $default_options = [
            'storeCategoryList' => [],
            'navigationId' => 1,
            'uid' => ''
        ];
        $options = array_merge($default_options, $options);

        // 如果没有指定用户UID，则生成一个随机UID
        if (empty($options['uid'])) {
            $options['uid'] = sprintf("%d", rand(1000000000000, 9999999999999));
        }

        // 处理默认商店分类列表
        if (empty($options['storeCategoryList'])) {
            $options['storeCategoryList'] = [
                ["storeType" => 256, "storeId" => 6758, "storeDeliveryAttr" => [3, 4, 6, 14, 12, 5, 2, 9, 13]],
                ["storeType" => 2, "storeId" => 6505, "storeDeliveryAttr" => [7]],
                ["storeType" => 4, "storeId" => 6672, "storeDeliveryAttr" => [3, 4]],
                ["storeType" => 8, "storeId" => 9992, "storeDeliveryAttr" => [1]]
            ];
        }

        // 构建请求参数
        $request_data = [
            'storeCategoryList' => $options['storeCategoryList'],
            'groupingId' => $groupingId,
            'navigationId' => $options['navigationId'],
            'uid' => $options['uid']
        ];

        // 转换请求参数为JSON格式
        $post_data = json_encode($request_data);

        // 生成加密请求头
        $header = $this->header_encrypt($post_data, $token, $device_id, 'android', '114.011671', '22.544368');

        // API请求地址
        $ajax_url = $this->domain . '/api/v1/sams/goods-portal/grouping/queryChildren';

        // 发送请求
        $ret = $this->http_curl($ajax_url, ['method' => 'POST', 'post' => $post_data, 'Header' => $header, 'ip' => $ip]);

        return $ret;
    }

    /*
     * 获取门店列表
     * @param string $token 用户令牌
     * @param string $device_id 设备ID
     * @param string $ip 代理IP
     * @param array $options 可选参数
     *   - storeId: 门店ID，默认为0获取所有门店
     * @return string 门店列表数据（JSON字符串）
     */
    public function get_store_list(string $token = '', string $device_id = '', string $ip = '', array $options = [])
    {
        // 设置默认参数
        $default_options = [
            'storeId' => 0
        ];
        $options = array_merge($default_options, $options);

        // 构建请求数据
        $request_data = [
            'storeId' => $options['storeId']
        ];

        $post_data = json_encode($request_data);
        $header = $this->header_encrypt($post_data, $token, $device_id);
        $ajax_url = $this->domain . '/api/v1/sams/merchant/storeApi/getBackStoreAddressList';
        $ret = $this->http_curl($ajax_url, ['method' => 'POST', 'post' => $post_data, 'Header' => $header, 'ip' => $ip]);
        return $ret;
    }

    /*
     * 获取云仓列表
     * @param string $token 用户令牌
     * @param string $device_id 设备ID
     * @param string $ip 代理IP
     * @param array $options 附加选项，可包含promotionId
     * @return mixed
     */
    public function get_stores_ticket(string $token = '', string $device_id = '', string $ip = '', array $options = [])
    {
        // 设置默认参数
        $default_options = [
            'promotionId' => '2066698172135942553',
            'locationStoreList' => []
        ];
        $options = array_merge($default_options, $options);

        // 构建请求数据
        $request_data = [
            'promotionId' => $options['promotionId'],
            'locationStoreList' => $options['locationStoreList']
        ];

        $post_data = json_encode($request_data);
        $header = $this->header_encrypt($post_data, $token, $device_id);
        $ajax_url = $this->domain . '/api/v1/sams/promotion/storesTicket';
        $ret = $this->http_curl($ajax_url, ['method' => 'POST', 'post' => $post_data, 'Header' => $header, 'ip' => $ip]);
        return $ret;
    }

    /**
     * 获取门店分类商品列表
     * @param int $storeId 门店ID
     * @param string $token 用户令牌
     * @param string $device_id 设备ID
     * @param string $ip 代理IP
     * @param array $options 附加选项，可包含categoryId
     * @return mixed
     */
    public function get_category_goods(int $storeId, string $token = '', string $device_id = '', string $ip = '', array $options = [])
    {
        // 设置默认参数
        $default_options = [
            'categoryId' => '',
            'pageSize' => 20,
            'pageNum' => 1,
            'useNewPage' => true,
            'useNew' => true,
            'isReversOrder' => false,
            'isFastDelivery' => false,
            'addressVO' => [
                'cityName' => '',
                'countryName' => '',
                'detailAddress' => '.',
                'districtName' => '',
                'provinceName' => ''
            ],
            'storeInfoVOList' => [],
            'deliveryType' => [1],
            'uid' => '',
            'recommendFirstCategoryId' => 0,
            'recommendSecondCategoryId' => 0,
            'frontCategoryIds' => [],
            'secondCategoryId' => 0,
            'isShowCustomTag' => true,
            'longitude' => '',
            'latitude' => ''
        ];
        $options = array_merge($default_options, $options);

        // 如果没有指定用户UID，则生成一个随机UID
        if(empty($options['uid'])){
            $options['uid'] = sprintf("%d", rand(1000000000000, 9999999999999));
        }

        // 如果有设置categoryId，则使用它来设置二级分类ID和推荐分类ID
        if (!empty($options['categoryId'])) {
            // 如果没有指定二级分类ID，使用传入的分类ID
            if ($options['secondCategoryId'] === 0) {
                $options['secondCategoryId'] = $options['categoryId'];
            }

            // 设置推荐分类ID
            if ($options['recommendSecondCategoryId'] === 0) {
                $options['recommendSecondCategoryId'] = $options['categoryId'];
            }
        }

        // 处理默认门店信息
        if (empty($options['storeInfoVOList'])) {
            $options['storeInfoVOList'] = [
                ["storeType" => 256, "storeId" => 6758, "storeDeliveryAttr" => [9, 13]],
                ["storeType" => 2, "storeId" => 6513, "storeDeliveryAttr" => [3, 4, 6, 14, 5, 12, 2, 7, 13]],
                ["storeType" => 4, "storeId" => $storeId, "storeDeliveryAttr" => [3, 4]],
                ["storeType" => 8, "storeId" => 9992, "storeDeliveryAttr" => [1]]
            ];
        }

        // 构建请求数据
        $request_data = [
            'pageSize' => $options['pageSize'],
            'pageNum' => $options['pageNum'],
            'useNewPage' => $options['useNewPage'],
            'useNew' => $options['useNew'],
            'isReversOrder' => $options['isReversOrder'],
            'isFastDelivery' => $options['isFastDelivery'],
            'addressVO' => $options['addressVO'],
            'storeInfoVOList' => $options['storeInfoVOList'],
            'deliveryType' => $options['deliveryType'],
            'uid' => $options['uid'],
            'recommendFirstCategoryId' => $options['recommendFirstCategoryId'],
            'recommendSecondCategoryId' => $options['recommendSecondCategoryId'],
            'frontCategoryIds' => $options['frontCategoryIds'],
            'secondCategoryId' => $options['secondCategoryId'],
            'isShowCustomTag' => $options['isShowCustomTag']
        ];

        $post_data = json_encode($request_data);
        $header = $this->header_encrypt($post_data, $token, $device_id, 'android', $options['longitude'], $options['latitude']);
        $ajax_url = $this->domain . '/api/v1/sams/goods-portal/grouping/list';
        $ret = $this->http_curl($ajax_url, ['method' => 'POST', 'post' => $post_data, 'Header' => $header, 'ip' => $ip]);
        return $ret;
    }

    /*
     * 提交订单
     * @param string $token 用户令牌
     * @param string $device_id 设备ID
     * @param string $ip 代理IP
     * @param array $options 可选参数
     *   - uid: 用户UID，默认随机生成
     *   - currency: 币种，默认CNY
     *   - settleInfoList: 结算信息列表，默认为空数组
     *   - addressId: 地址ID，默认151542723
     *   - channel: 支付渠道，默认alipay
     *   - hkShopingProtocol: 港购协议，默认true
     *   - totalAmount: 总金额，默认0
     *   - payType: 支付类型，默认0
     *   - payMethodId: 支付方式ID，默认空
     *   - gray: 是否灰度，默认true
     *   - isSelectShoppingNotes: 是否选择购物须知，默认true
     *   - useCash: 是否使用现金，默认false
     *   - cashInfo: 现金信息，默认空数组
     *   - tongDunReportData: 同盾风控数据，默认空字符串
     */
    public function commit_pay(string $token, string $device_id = '', string $ip = '', array $options = [])
    {
        // 设置默认参数
        $default_options = [
            'uid' => '',
            'currency' => 'CNY',
            'appId' => '',
            'settleInfoList' => [],
            'addressId' => 151542723,
            'channel' => 'alipay',
            'hkShopingProtocol' => true,
            'totalAmount' => 0,
            'payType' => 0,
            'payMethodId' => '',
            'gray' => true,
            'isSelectShoppingNotes' => true,
            'useCash' => false,
            'cashInfo' => [],
            'tongDunReportData' => ''
        ];
        $options = array_merge($default_options, $options);
        if (empty($options['uid'])) {
            $options['uid'] = sprintf("%d", rand(1000000000000, 9999999999999));
        }
        $request_data = [
            'uid' => $options['uid'],
            'appId' => $options['appId'],
            'currency' => $options['currency'],
            'settleInfoList' => $options['settleInfoList'],
            'addressId' => $options['addressId'],
            'channel' => $options['channel'],
            'hkShopingProtocol' => $options['hkShopingProtocol'],
            'totalAmount' => $options['totalAmount'],
            'payType' => $options['payType'],
            'payMethodId' => $options['payMethodId'],
            'gray' => $options['gray'],
            'isSelectShoppingNotes' => $options['isSelectShoppingNotes'],
            'useCash' => $options['useCash'],
            'cashInfo' => $options['cashInfo'],
            'tongDunReportData' => $options['tongDunReportData']
        ];
        $post_data = json_encode($request_data);
        $header = $this->header_encrypt($post_data, $token, $device_id);
        $ajax_url = $this->domain . '/api/v3/sams/trade/settlement/commitPay';
        $ret = $this->http_curl($ajax_url, ['method' => 'POST', 'post' => $post_data, 'Header' => $header, 'ip' => $ip]);
        return $ret;
    }

    /*
     * 获取支付方式
     * @param string $token 用户令牌
     * @param string $device_id 设备ID
     * @param string $ip 代理IP
     * @param array $options 可选参数
     *   - list: 支付方式请求列表，默认为空数组
     */
    public function get_payment_methods(string $token, string $device_id = '', string $ip = '', array $options = [])
    {
        // 设置默认参数
        $default_options = [
            'list' => []
        ];
        $options = array_merge($default_options, $options);
        $request_data = [
            'list' => $options['list']
        ];
        $post_data = json_encode($request_data);
        $header = $this->header_encrypt($post_data, $token, $device_id, 'android', '113.727975', '22.979352');
        $ajax_url = $this->domain . '/api/v1/sams/configuration/paymentStore4C/queryList2';
        $ret = $this->http_curl($ajax_url, ['method' => 'POST', 'post' => $post_data, 'Header' => $header, 'ip' => $ip]);
        return $ret;
    }

    /*
     * 更改配送类型
     * @param string $token 用户令牌
     * @param string $device_id 设备ID
     * @param string $ip 代理IP
     * @param array $options 可选参数
     *   - changeDeliveryTypeList: 更改配送类型列表，默认为空数组
     *   - storeInfoList: 门店信息列表，默认为空数组
     */
    public function change_delivery_type(string $token, string $device_id = '', string $ip = '', array $options = [])
    {
        // 设置默认参数
        $default_options = [
            'changeDeliveryTypeList' => [],
            'storeInfoList' => []
        ];
        $options = array_merge($default_options, $options);
        // 处理默认门店列表
        if (empty($options['storeInfoList'])) {
            $options['storeInfoList'] = [
                [
                    "storeId" => 6758,
                    "storeType" => 256,
                    "areaBlockId" => 42536,
                    "storeDeliveryTemplateId" => 626657353395258646,
                    "deliveryModeId" => 1006
                ],
                [
                    "storeId" => 6570,
                    "storeType" => 2,
                    "areaBlockId" => 2353958028014939670,
                    "storeDeliveryTemplateId" => 2581297327109190166,
                    "deliveryModeId" => 1019
                ],
                [
                    "storeId" => 5390,
                    "storeType" => 4,
                    "areaBlockId" => 2353958028014939670,
                    "storeDeliveryTemplateId" => 2384372897121755926,
                    "deliveryModeId" => 1009
                ],
                [
                    "storeId" => 9992,
                    "storeType" => 8,
                    "areaBlockId" => 42305,
                    "storeDeliveryTemplateId" => 1743371277198079766,
                    "deliveryModeId" => 1010
                ]
            ];
        }
        $request_data = [
            'changeDeliveryTypeList' => $options['changeDeliveryTypeList'],
            'storeInfoList' => $options['storeInfoList']
        ];
        $post_data = json_encode($request_data);
        $header = $this->header_encrypt($post_data, $token, $device_id);
        $ajax_url = $this->domain . '/api/v1/sams/trade/cart/changeDeliveryType';
        $ret = $this->http_curl($ajax_url, ['method' => 'POST', 'post' => $post_data, 'Header' => $header, 'ip' => $ip]);
        return $ret;
    }

    /*
     * 删除购物车商品
     * @param string $token 用户令牌
     * @param string $device_id 设备ID
     * @param string $ip 代理IP
     * @param array $options 可选参数
     *   - cartGoodsList: 购物车商品列表，默认为空数组
     *   - userUid: 用户UID，默认随机生成
     *   - visitorId: 访客ID，默认为空
     */
    public function delete_cart_goods(string $token, string $device_id = '', string $ip = '', array $options = [])
    {
        // 设置默认参数
        $default_options = [
            'cartGoodsList' => [],
            'userUid' => '',
            'visitorId' => ''
        ];
        $options = array_merge($default_options, $options);
        if (empty($options['userUid'])) {
            $options['userUid'] = sprintf("%d", rand(1000000000000, 9999999999999));
        }
        $request_data = [
            'cartGoodsList' => $options['cartGoodsList'],
            'deviceType' => 'android',
            'uid' => $options['userUid'],
            'visitorId' => $options['visitorId']
        ];
        $post_data = json_encode($request_data);
        $header = $this->header_encrypt($post_data, $token, $device_id);
        $ajax_url = $this->domain . '/api/v1/sams/trade/cart/batchDelGoods';
        $ret = $this->http_curl($ajax_url, ['method' => 'POST', 'post' => $post_data, 'Header' => $header, 'ip' => $ip]);
        return $ret;
    }

    /*
     * 添加收货地址
     * @param string $token 用户token
     * @param string $device_id 设备ID
     * @param string $ip 代理IP
     * @param array $options 可选参数
     * @return string 添加地址结果（JSON字符串）
     */
    public function add_receiver_address(string $token, string $device_id = '', string $ip = '', array $options = [])
    {
        $post_data = json_encode([
            'mobile' => $options['mobile'] ?? '',
            'name' => $options['name'] ?? '',
            'countryCode' => $options['countryCode'] ?? '+86',
            'streetName' => $options['streetName'] ?? '',
            'isDefault' => $options['isDefault'] ?? 2,
            'receiverAddress' => $options['receiverAddress'] ?? '',
            'cityName' => $options['cityName'] ?? '',
            'detailAddress' => $options['detailAddress'] ?? '',
            'districtName' => $options['districtName'] ?? '',
            'latitude' => $options['latitude'] ?? 0,
            'longitude' => $options['longitude'] ?? 0,
            'provinceName' => $options['provinceName'] ?? '',
            'addressTag' => $options['addressTag'] ?? '',
            'mapAddress' => $options['mapAddress'] ?? ''
        ]);

        return $this->request('/api/v1/sams/sams-user/receiver_address/add', $post_data, $token, $device_id, $ip);
    }

    /*
     * 更新购物车商品数量
     * @param string $spuId 商品ID
     * @param string $storeId 门店ID
     * @param string $token 用户token
     * @param string $device_id 设备ID
     * @param string $ip 代理IP
     * @param array $options 可选参数
     * @return string 更新购物车结果（JSON字符串）
     */
    public function update_cart_item(string $spuId, string $storeId, string $token, string $device_id = '', string $ip = '', array $options = [])
    {
        $post_data = json_encode([
            'spuId' => $spuId,
            'storeId' => $storeId,
            'quantity' => $options['quantity'] ?? 1
        ]);

        return $this->request('/api/v1/sams/trade/cart/update', $post_data, $token, $device_id, $ip);
    }

    /*
     * 删除收货地址
     * @param int $addressId 地址ID
     * @param string $token 用户token
     * @param string $device_id 设备ID
     * @param string $ip 代理IP
     * @return string 删除地址结果（JSON字符串）
     */
    public function delete_receiver_address(int $addressId, string $token, string $device_id = '', string $ip = '')
    {
        $post_data = json_encode(['addressId' => $addressId]);
        $header = $this->header_encrypt($post_data, $token, $device_id);
        $ajax_url = $this->domain . '/api/v1/sams/sams-user/receiver_address/delete';
        $ret = $this->http_curl($ajax_url, ['method'=>'POST','post'=>$post_data,'Header'=>$header,'ip'=>$ip]);
        return $ret;
    }

    /*
     * 未登录状态下获取用户绑定设备信息
     * @param string $phone 手机号
     * @param string $code 验证码
     * @param string $ip 代理IP
     * @param string $device_id 设备ID，默认为空
     * @return string 设备ID
     */
    public function not_login_get_member_bind_device(string $phone, string $code, string $ip = '', string $device_id = '')
    {

        // 验证手机号格式并获取国家代码
        $validatePhoneNumber = $this->validatePhoneNumber($phone);
        $countryCode = $validatePhoneNumber['area_code'];

        // 对手机号和验证码进行加密
        $encryptedMobile = $this->AES_Encrypt($phone);
        $encryptedSmsCode = $this->AES_Encrypt($code);

        // 构建请求数据
        $post_data = json_encode([
            'type' => '0',
            'mobile' => $encryptedMobile,
            'smsCode' => $encryptedSmsCode,
            'countryCode' => $countryCode
        ]);

        // 生成请求头
        $header = $this->header_encrypt($post_data, '', $device_id);

        // API请求地址
        $ajax_url = $this->domain . '/api/v1/sams/sams-user/device/not_login/get_member_bind_device';

        // 发送请求
        $response = $this->http_curl($ajax_url, ['method'=>'POST','post'=>$post_data,'Header'=>$header,'ip'=>$ip]);

        // 解析响应
        $ret = json_decode($response, true);

        // 如果请求失败但不是因为用户未绑定会员卡，则可能是设备ID问题
        if ($ret !== null && isset($ret['success']) && $ret['success'] === true) {
            // 取最后一个数组
            $ret['data'] = array_reverse($ret['data']);
            $device_id = $ret['data'][0]['deviceId'] ?? '';
        }
        // 返回设备ID，无论请求成功与否
        return $device_id;
    }

    /**
     * 验证手机号格式并返回国家代码
     *
     * @param string $phone 手机号（纯数字）
     * @return array 包含处理后的手机号和国家代码
     */
    private function validatePhoneNumber(string $phone): array
    {
        // 移除所有非数字字符
        $phone = preg_replace('/\D/', '', $phone);

        // 默认使用中国区号
        $area_code = '+86';

        // 简单判断：中国手机号11位，香港手机号8位
        if (strlen($phone) == 11) {
            $area_code = '+86'; // 中国
        } elseif (strlen($phone) == 8) {
            $area_code = '+852'; // 香港
        }

        return [
            'phone' => $phone,
            'area_code' => $area_code
        ];
    }

    /**
     * 获取系统配置信息
     *
     * @param string $token 用户令牌，可选
     * @param string $device_id 设备ID，可选
     * @param string $ip 代理IP，可选
     * @param string $keyId 配置键ID，默认为"info"
     * @return string 配置信息（JSON字符串）
     */
    public function get_config(string $token = '', string $device_id = '', string $ip = '', string $keyId = 'info')
    {
        // 构建请求数据
        $post_data = json_encode(['keyId' => $keyId]);

        // 生成加密请求头
        $header = $this->header_encrypt($post_data, $token, $device_id);

        // API请求地址
        $ajax_url = $this->domain . '/api/v1/sams/configuration/portal/getConfig';

        // 发送请求
        $ret = $this->http_curl($ajax_url, ['method' => 'POST', 'post' => $post_data, 'Header' => $header, 'ip' => $ip]);

        return $ret;
    }

    /**
     * 获取商品详情
     *
     * 该方法用于获取指定商品的详细信息，包括标题、价格、库存、描述等
     *
     * @param string $spuId 商品ID
     * @param string $token 用户令牌
     * @param string $device_id 设备ID
     * @param string $ip 代理IP
     * @param array $options 可选参数
     *   - storeId: 门店ID，默认为6758
     *   - addressVO: 地址信息，默认为空对象
     *   - areaBlockId: 区域块ID，默认为42305
     *   - storeDeliveryTemplateId: 门店配送模板ID，默认为1845618165690835734
     *   - storeInfoVOList: 门店信息列表，默认为预设列表
     *   - uid: 用户UID，默认随机生成
     *   - longitude: 经度，默认为113.296973
     *   - latitude: 纬度，默认为23.345214
     *
     * @return string 商品详情数据（JSON字符串）
     */
    public function get_product_detail(string $spuId, string $token = '', string $device_id = '', string $ip = '', array $options = [])
    {
        // 设置默认参数
        $default_options = [
            'storeId' => 6518,
            'addressVO' => [
                'cityName' => '',
                'countryName' => '',
                'districtName' => '',
                'provinceName' => ''
            ],
            'areaBlockId' => 1,
            'storeDeliveryTemplateId' => 0,
            'storeInfoVOList' => [],
            'uid' => '',
            'longitude' => '',
            'latitude' => ''
        ];

        // 合并用户提供的选项与默认选项
        $options = array_merge($default_options, $options);

        // 如果没有指定用户UID，则生成一个随机UID
        if (empty($options['uid'])) {
            $options['uid'] = sprintf("%d", rand(1000000000000, 9999999999999));
        }

        // 处理默认门店信息列表
        if (empty($options['storeInfoVOList'])) {
            //
            $options['storeInfoVOList'] = [
                ["storeType" => 256, "storeId" => 6758, "storeDeliveryAttr" => [9, 13]],
                ["storeType" => 2, "storeId" => 6513, "storeDeliveryAttr" => [3, 4, 6, 14, 5, 12, 2, 7, 13]],
                ["storeType" => 4, "storeId" => $options['storeId'], "storeDeliveryAttr" => [3, 4]],
                ["storeType" => 8, "storeId" => 9992, "storeDeliveryAttr" => [1]]
            ];
        }

        // 构建请求参数
        $request_data = [
            'source' => 'ANDROID',
            'channel' => 1,
            'spuId' => $spuId,
            'uid' => $options['uid'],
            'addressVO' => $options['addressVO'],
            'areaBlockId' => $options['areaBlockId'],
            'storeDeliveryTemplateId' => $options['storeDeliveryTemplateId'],
            'storeId' => $options['storeId'],
            'storeInfoVOList' => $options['storeInfoVOList']
        ];

        // 转换请求参数为JSON格式
        $post_data = json_encode($request_data);

        // 生成加密请求头
        $header = $this->header_encrypt($post_data, $token, $device_id, 'android', $options['longitude'], $options['latitude']);

        // API请求地址
        $ajax_url = $this->domain . '/api/v1/sams/goods-portal/spu/queryDetail';

        // 发送请求
        $ret = $this->http_curl($ajax_url, ['method' => 'POST', 'post' => $post_data, 'Header' => $header, 'ip' => $ip]);

        return $ret;
    }

    // 获取同盾指纹
    private function generateTongDunReportData(string $type = ''): string
    {
        // 如果type不为空，则请求接口获取指纹数据
        if (!empty($type)) {
            $url = "http://**************:3000/risk-params?type=" . $type;
            $res = $this->http_curl($url, ['method' => 'GET']);
            $res = json_decode($res, true);
            if (isset($res['data'])) {
                return $res['data'];
            }
        }

        // 否则虚拟生成指纹数据
        // 生成随机位置信息
        $longitude = rand(1000000, 1200000) / 10000;
        $latitude = rand(200000, 300000) / 10000;
        $province = '广东';
        $city = '深圳';
        $address = $province . $city;

        // 生成唯一标识符
        $uuid = uniqid(rand(10000, 99999), true);
        // 随机设备类型
        $appType = rand(0, 1) ? 'ios' : 'android';
        $appChannel = $appType === 'ios' ? 'iOS' : 'Android';
        // 直接生成简单的blackbox字符串，格式类似于 pMPVf17529922804NLsMiVD7n4
        $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        $blackbox = $chars[rand(0, 51)]; // 字母开头
        $numPosition = rand(5, 8);
        // 添加前缀部分随机字符
        for ($i = 1; $i < $numPosition; $i++) {
            $blackbox .= $chars[rand(0, strlen($chars) - 1)];
        }
        // 添加一段数字
        $blackbox .= rand(10000000, 99999999999);
        // 添加后缀部分随机字符
        $suffixLength = rand(5, 10);
        for ($i = 0; $i < $suffixLength; $i++) {
            $blackbox .= $chars[rand(0, strlen($chars) - 1)];
        }
        // 构建数据对象
        $data = [
            'gpsAddr' => $address,
            'appChannel' => $appChannel,
            'UUID' => $uuid,
            'gpsProv' => $province,
            'blackbox' => $blackbox,
            'localid' => '',
            'appType' => $appType,
            'gpsCity' => $city
        ];

        // 将数据转换为JSON并进行Base64编码
        return base64_encode(json_encode($data));
    }

    /**
     * 获取系统消息列表
     *
     * 该方法用于获取用户的系统消息列表，包括会籍账号、权限等相关消息
     *
     * @param string $token 用户令牌
     * @param string $device_id 设备ID
     * @param string $ip 代理IP
     * @param array $options 可选参数
     *   - pageNum: 页码，默认为1
     *   - pageSize: 每页条数，默认为10
     *   - uid: 用户ID，默认为空
     *   - type: 消息类型，默认为6
     *   - storeIdList: 门店ID列表，默认为[6758,4806,5352,9996]
     *   - isMarkRead: 是否标记为已读，默认为false
     *   - channel: 渠道，默认为"1"
     *   - appVersion: 应用版本，默认为"5.0.124"
     *   - appIO: 应用平台，默认为"android"
     *
     * @return string 系统消息列表数据（JSON字符串）
     */
    public function get_system_message_list(string $token, string $device_id = '', string $ip = '', array $options = [])
    {
        // 设置默认参数
        $default_options = [
            'pageNum' => 1,
            'pageSize' => 10,
            'uid' => '',
            'type' => 6,
            'storeIdList' => [],
            'isMarkRead' => false,
            'channel' => '1',
            'appVersion' => '5.0.124',
            'appIO' => 'android'
        ];

        // 合并用户提供的选项与默认选项
        $options = array_merge($default_options, $options);

        // 如果没有指定用户UID，则生成一个随机UID
        if (empty($options['uid'])) {
            $options['uid'] = '1818' . rand(10000000, 99999999);
        }

        // 构建请求参数
        $request_data = [
            'pageNum' => $options['pageNum'],
            'pageSize' => $options['pageSize'],
            'queryParameter' => [
                'type' => $options['type'],
                'uid' => $options['uid'],
                'storeIdList' => $options['storeIdList'],
                'isMarkRead' => $options['isMarkRead'],
                'channel' => $options['channel']
            ],
            'appVersion' => $options['appVersion'],
            'appIO' => $options['appIO']
        ];

        // 转换请求参数为JSON格式
        $post_data = json_encode($request_data);

        // 生成加密请求头
        $header = $this->header_encrypt($post_data, $token, $device_id);
        // API请求地址
        $ajax_url = $this->domain . '/api/v1/sams/message/portal/systemMessage/getSystemMessageList';

        // 发送请求
        $ret = $this->http_curl($ajax_url, ['method' => 'POST', 'post' => $post_data, 'Header' => $header, 'ip' => $ip]);

        return $ret;
    }

    /**
     * 生成副卡邀请密钥
     *
     * 该方法用于生成会员副卡的邀请密钥，可用于邀请家人成为副卡会员
     *
     * @param string $token 用户令牌
     * @param string $device_id 设备ID
     * @param string $ip 代理IP
     *
     * @return string 副卡邀请密钥数据（JSON字符串）
     */
    public function create_family_card_invitation(string $token, string $device_id = '', string $ip = '')
    {
        // 构建空的请求参数，API不需要额外参数
        $post_data = '{}';
        // 生成加密请求头
        $header = $this->header_encrypt($post_data, $token, $device_id);
        // API请求地址
        $ajax_url = $this->domain . '/api/v1/sams/sams-user/membership/gift_card/create_invitation';
        // 发送请求
        $ret = $this->http_curl($ajax_url, ['method' => 'POST', 'post' => $post_data, 'Header' => $header, 'ip' => $ip]);

        return $ret;
    }

    /**
     * 绑定副卡密钥
     *
     * 该方法用于绑定会员副卡的邀请密钥，成为某个主卡会员的副卡会员
     *
     * @param string $donorInfo 主卡会员的加密信息
     * @param string $mobile 手机号
     * @param string $realName 真实姓名
     * @param string $idNo 身份证号
     * @param string $token 用户令牌
     * @param string $device_id 设备ID
     * @param string $ip 代理IP
     * @param array $options 可选参数
     *   - countryCode: 国家代码，默认为"+86"
     *   - idType: 证件类型，默认为1（身份证）
     *   - provinceName: 省份名称，默认为"广东省"
     *   - cityName: 城市名称，默认为"深圳市"
     *   - districtName: 区域名称，默认为"龙岗区"
     *   - streetName: 街道名称，默认为空
     *   - detailAddress: 详细地址，默认为空
     *   - latitude: 纬度，默认为22.637217
     *   - longitude: 经度，默认为114.18642
     *   - regType: 注册类型，默认为"familyCard"
     *   - agreementConfirm: 是否同意协议，默认为true
     *   - agree: 是否同意，默认为true
     *   - tongDunType: 同盾风控类型，默认为"mini"
     *
     * @return string 绑定结果（JSON字符串）
     */
    public function register_family_card(string $donorInfo, string $mobile, string $realName, string $idNo, string $token = '', string $device_id = '', string $ip = '', array $options = [])
    {
        // 设置默认参数
        $default_options = [
            'countryCode' => '+86',
            'idType' => 1,
            'provinceName' => '北京市',
            'cityName' => '北京市',
            'districtName' => '北京市',
            'streetName' => '北京市',
            'detailAddress' => '北京市',
            'latitude' => 0.0,
            'longitude' => 0.0,
            'regType' => 'familyCard',
            'agreementConfirm' => true,
            'agree' => true,
            'tongDunType' => 'mini'
        ];

        // 合并用户提供的选项与默认选项
        $options = array_merge($default_options, $options);

        // 生成同盾风控数据
        $tongDunReportData = $this->generateTongDunReportData($options['tongDunType']);

        // 构建请求参数
        $request_data = [
            'donorInfo' => $donorInfo,
            'regType' => $options['regType'],
            'mobile' => $mobile,
            'countryCode' => $options['countryCode'],
            'idType' => $options['idType'],
            'agreementConfirm' => $options['agreementConfirm'],
            'realName' => $realName,
            'idNo' => $idNo,
            'provinceName' => $options['provinceName'],
            'cityName' => $options['cityName'],
            'districtName' => $options['districtName'],
            'latitude' => $options['latitude'],
            'longitude' => $options['longitude'],
            'streetName' => $options['streetName'],
            'agree' => $options['agree'],
            'detailAddress' => $options['detailAddress'],
            'tongDunReportData' => $tongDunReportData
        ];

        // 转换请求参数为JSON格式
        $post_data = json_encode($request_data);

        // 生成加密请求头
        $header = $this->header_encrypt($post_data, $token, $device_id);

        // API请求地址
        $ajax_url = $this->domain . '/api/v1/sams/sams-user/membership/gift_card/register';

        // 发送请求
        $ret = $this->http_curl($ajax_url, ['method' => 'POST', 'post' => $post_data, 'Header' => $header, 'ip' => $ip]);

        return $ret;
    }

    /**
     * AB測試報告
     *
     * 該方法用於向山姆會員商店報告AB測試數據，用於風控驗證和用戶行為分析
     *
     * @param string $token 用戶令牌
     * @param string $device_id 設備ID
     * @param string $ip 代理IP
     * @param array $options 可選參數
     *   - businessCode: 業務代碼，默認為"9191"
     *   - expId: 實驗ID，默認為"794"
     *   - expKey: 實驗鍵，默認為"exp_3dtouch_C"
     *   - groupKey: 分組鍵，默認為"exp_3dtouch"
     *   - kaName: 應用名稱，默認為"SAMS"
     *   - layerKey: 層級鍵，默認為"exp_3dtouch"
     *   - params: 參數對象，默認為空對象{}
     *   - qimei: 設備指紋，默認為空字符串
     *   - reportPath: 報告路徑，默認為"SAMS_online_widget3DTouchExp_exp_3dtouch"
     *   - userType: 用戶類型，默認為2
     *   - timestamp: 時間戳，默認為當前時間戳
     *
     * @return string AB測試報告結果（JSON字符串）
     */
    public function abtest_report(string $token, string $device_id = '', string $ip = '', array $options = [])
    {
        // 設置默認參數
        $default_options = [
            'businessCode' => '9191',
            'expId' => '794',
            'expKey' => 'exp_3dtouch_C',
            'groupKey' => 'exp_3dtouch',
            'kaName' => 'SAMS',
            'layerKey' => 'exp_3dtouch',
            'params' => (object)[], // 使用空對象而不是空數組
            'qimei' => '',
            'reportPath' => 'SAMS_online_widget3DTouchExp_exp_3dtouch',
            'userType' => 2,
            'timestamp' => $this->getUnixTimestamp()
        ];

        // 合併用戶提供的選項與默認選項
        $options = array_merge($default_options, $options);

        // 構建請求參數（單個對象，不是數組）
        $request_data = [
            'businessCode' => $options['businessCode'],
            'expId' => $options['expId'],
            'expKey' => $options['expKey'],
            'groupKey' => $options['groupKey'],
            'kaName' => $options['kaName'],
            'layerKey' => $options['layerKey'],
            'params' => $options['params'],
            'qimei' => $options['qimei'],
            'reportPath' => $options['reportPath'],
            'userType' => $options['userType'],
            'timestamp' => $options['timestamp']
        ];

        // 轉換請求參數為JSON格式（包裝在數組中）
        $post_data = json_encode([$request_data]);

        // 生成加密請求頭
        $header = $this->header_encrypt($post_data, $token, $device_id);

        // API請求地址
        $ajax_url = $this->domain . '/api/v1/sams/configuration/abtest/portal/report';

        // 發送請求
        $ret = $this->http_curl($ajax_url, ['method' => 'POST', 'post' => $post_data, 'Header' => $header, 'ip' => $ip]);

        return $ret;
    }
}