---
description: 使用示例
globs: 
alwaysApply: false
---
# 使用示例

以下是SamClub类的一些常见使用场景示例：

## 用户登录流程

```php
// 创建SamClub实例
$sam = new SamClub();

// 发送验证码
$phone = '13800138000';
$sam->send_code($phone);

// 使用验证码登录
$code = '123456';
$login_result = $sam->mobile($phone, $code);
$login_data = json_decode($login_result, true);

// 获取token
$token = $login_data['data']['authToken'];

// 检查登录状态
$is_logged_in = $sam->check_login($token);
```

## 商品搜索与购物车操作

```php
// 搜索商品
$keyword = '牛奶';
$search_result = $sam->search_goods($keyword, $token);
$search_data = json_decode($search_result, true);

// 添加商品到购物车
$spuId = 1234567;
$storeId = 5390;
$sam->add_to_cart($spuId, $storeId, $token);

// 获取购物车
$cart_result = $sam->get_cart($token);
$cart_data = json_decode($cart_result, true);
```

## 订单结算流程

```php
// 获取预结算信息
$pre_settle_result = $sam->get_pre_settle_info($token);
$pre_settle_data = json_decode($pre_settle_result, true);

// 获取结算单
$settle_result = $sam->get_settle_info($token);
$settle_data = json_decode($settle_result, true);

// 获取配送时间
$capacity_result = $sam->get_capacity_data($token);
$capacity_data = json_decode($capacity_result, true);

// 提交订单
$commit_result = $sam->commit_pay($token);
$commit_data = json_decode($commit_result, true);
```

## 地址管理

```php
// 获取地址列表
$address_list_result = $sam->get_address_list($token);
$address_list_data = json_decode($address_list_result, true);

// 添加地址
$address_options = [
    'mobile' => '13800138000',
    'name' => '张三',
    'cityName' => '深圳市',
    'districtName' => '南山区',
    'detailAddress' => '科技园路1号',
    'provinceName' => '广东省',
    'latitude' => 22.53332,
    'longitude' => 113.93041
];
$sam->add_receiver_address($token, '', '', $address_options);
```
