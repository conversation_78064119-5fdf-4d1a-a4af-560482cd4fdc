---
description: API数据格式规则
globs: 
alwaysApply: false
---
# API数据格式规则

`数据包`目录中包含了与山姆会员商店API交互的请求和响应数据示例，这些文件对于理解API的请求格式和响应结构非常有价值。

## 数据包文件结构

每个数据包文件包含以下部分：
1. HTTP请求头 - 包含认证信息、设备信息等
2. 请求体 - JSON格式的请求参数
3. HTTP响应头 - 服务器返回的头信息
4. 响应体 - JSON格式的响应数据

## 重要的请求头参数

请求头中包含多个关键参数，这些参数在`header_encrypt`方法中生成：

- `auth-token` - 用户认证令牌
- `device-id` - 设备ID
- `device-name` - 设备名称
- `device-type` - 设备类型
- `device-os-version` - 设备操作系统版本
- `st` - 签名
- `n` - 加密参数
- `t` - 时间戳
- `longitude`/`latitude` - 经纬度位置信息

## 响应数据结构

API响应通常包含以下字段：

```json
{
  "data": { /* 主要数据 */ },
  "code": "Success", 
  "msg": "",
  "errorMsg": "",
  "traceId": "...",
  "requestId": "...",
  "rt": 0,
  "success": true
}
```

## 数据包示例

- [搜索商品.txt](mdc:数据包/搜索商品.txt) - 商品搜索API的请求和响应
- [添加购物车.txt](mdc:数据包/添加购物车.txt) - 添加商品到购物车的请求和响应
- [查看购物车.txt](mdc:数据包/查看购物车.txt) - 获取购物车数据的请求和响应
- [获取报价单.txt](mdc:数据包/获取报价单.txt) - 获取结算前报价单的请求和响应
- [提交订单.txt](mdc:数据包/提交订单.txt) - 提交订单的请求和响应

## 开发注意事项

1. 请求头中的加密参数需要通过`header_encrypt`方法生成
2. 请求体和响应体都使用JSON格式
3. 请求中的时间戳需要是毫秒级的Unix时间戳
4. 响应中的`success`字段表示请求是否成功
5. 错误信息会在`msg`或`errorMsg`字段中返回

