---
description: 山姆会员商店API项目概述
globs: 
alwaysApply: false
---
# 山姆会员商店API项目概述

这是一个用于与山姆会员商店API交互的PHP项目。项目提供了一系列方法来实现用户认证、商品搜索、购物车管理、订单处理等功能。

## 主要文件

- [SamClub.php](mdc:SamClub.php) - 主类文件，包含与山姆会员商店API交互的所有方法
- [SDK_Http.php](mdc:SDK_Http.php) - HTTP请求处理类，处理所有的API请求

## 项目结构

- `public/sams_key/` - 存储API密钥的目录
- `数据包/` - 包含各种API响应示例的目录，用于参考和测试

## 功能概述

该项目实现了以下主要功能：
- 用户登录与认证
- 商品搜索
- 购物车管理（添加、更新、删除商品）
- 地址管理
- 订单创建与支付
- 门店信息获取
