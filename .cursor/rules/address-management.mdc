---
description: 地址管理相关方法
globs: 
alwaysApply: false
---
# 地址管理相关方法

SamClub类提供了以下与地址管理相关的方法：

## 地址操作

- `get_address_list(string $token, string $device_id, string $ip)` - 获取用户地址列表
- `add_receiver_address(string $token, string $device_id, string $ip, array $options)` - 添加收货地址
- `delete_receiver_address(int $addressId, string $token, string $device_id, string $ip)` - 删除收货地址

## 地址相关的门店操作

- `get_recommend_store_list(float $latitude, float $longitude, string $token, string $device_id, string $ip)` - 根据地址获取附近推荐门店

## 地址参数说明

添加地址时的可选参数包括：

- `mobile` - 手机号
- `name` - 收货人姓名
- `countryCode` - 国家代码，默认'+86'
- `streetName` - 街道名称
- `isDefault` - 是否默认地址，默认为2
- `receiverAddress` - 收货地址
- `cityName` - 城市名称
- `detailAddress` - 详细地址
- `districtName` - 区域名称
- `latitude` - 纬度
- `longitude` - 经度
- `provinceName` - 省份名称
- `addressTag` - 地址标签
- `mapAddress` - 地图地址
