---
description: 实现新API方法指南
globs: 
alwaysApply: false
---
# 实现新API方法指南

本指南介绍如何根据`数据包`目录中的抓包数据，在`SamClub.php`中实现新的API方法。

## 实现步骤

1. **分析数据包文件**
   - 查看请求URL，确定API端点
   - 分析请求头，了解必要的认证参数
   - 检查请求体，确定必要的参数和格式
   - 分析响应体，了解返回数据结构

2. **确定方法参数**
   - 必选参数：通常包括`token`、`device_id`、`ip`等基础参数
   - 可选参数：根据API需求，设计为`$options`数组参数

3. **编写方法代码**
   - 定义方法签名和参数
   - 处理默认参数和可选参数
   - 构建请求数据
   - 调用`header_encrypt`生成加密请求头
   - 使用`http_curl`发送请求
   - 返回响应结果

## 代码模板

```php
/*
 * 方法名称
 * @param string $token 用户令牌
 * @param string $device_id 设备ID
 * @param string $ip 代理IP
 * @param array $options 可选参数
 */
public function method_name(string $token, string $device_id = '', string $ip = '', array $options = [])
{
    // 设置默认参数
    $default_options = [
        'param1' => 'default1',
        'param2' => 'default2'
    ];
    
    // 合并用户提供的选项与默认选项
    $options = array_merge($default_options, $options);
    
    // 构建请求参数
    $request_data = [
        'field1' => $options['param1'],
        'field2' => $options['param2']
    ];
    
    // 转换请求参数为JSON格式
    $post_data = json_encode($request_data);
    
    // 生成加密请求头
    $header = $this->header_encrypt($post_data, $token, $device_id);
    
    // API请求地址
    $ajax_url = $this->domain . '/api/path/to/endpoint';
    
    // 发送请求
    $ret = $this->http_curl($ajax_url, ['method' => 'POST', 'post' => $post_data, 'Header' => $header, 'ip' => $ip]);
    
    return $ret;
}
```

## 示例：实现搜索商品方法

以下是根据`搜索商品.txt`数据包实现的方法示例：

```php
public function search_goods(string $keyword, string $token = '', string $device_id = '', string $ip = '', array $options = [])
{
    // 设置默认参数
    $default_options = [
        'pageNum' => 1,
        'pageSize' => 20,
        'sort' => '0',
        'storeInfoList' => [],
        'address' => [],
        'userUid' => '',
        'uid' => '',
        'uidType' => 3
    ];
    
    // 合并用户提供的选项与默认选项
    $options = array_merge($default_options, $options);
    
    // 处理默认门店信息
    if (empty($options['storeInfoList'])) {
        $options['storeInfoList'] = [
            ["storeId" => 6758, "storeType" => 256, "storeDeliveryAttr" => [5, 6, 9, 12, 13, 14]],
            ["storeId" => 6570, "storeType" => 2, "storeDeliveryAttr" => [3, 4, 7, 13]],
            ["storeId" => 5390, "storeType" => 4, "storeDeliveryAttr" => [3, 4]],
            ["storeId" => 9992, "storeType" => 8, "storeDeliveryAttr" => [1]]
        ];
    }
    
    // 构建请求参数
    $request_data = [
        'userUid' => $options['userUid'],
        'pageNum' => $options['pageNum'],
        'pageSize' => $options['pageSize'],
        'keyword' => $keyword,
        'rewriteWord' => $keyword,
        'storeInfoVOList' => $options['storeInfoList'],
        'addressVO' => $options['address'],
        'uid' => $options['uid'],
        'uidType' => $options['uidType'],
        'sort' => $options['sort']
    ];
    
    // 转换请求参数为JSON格式
    $post_data = json_encode($request_data);
    
    // 生成加密请求头
    $header = $this->header_encrypt($post_data, $token, $device_id);
    
    // API请求地址
    $ajax_url = $this->domain . '/api/v1/sams/goods-portal/spu/search';
    
    // 发送请求
    $ret = $this->http_curl($ajax_url, ['method' => 'POST', 'post' => $post_data, 'Header' => $header, 'ip' => $ip]);
    
    return $ret;
}
```

## 注意事项

1. 确保参数命名与API一致
2. 提供合理的默认值
3. 处理可选参数和必选参数
4. 正确使用`header_encrypt`方法生成请求头
5. 使用`http_curl`方法发送请求
6. 添加适当的方法注释和参数说明

