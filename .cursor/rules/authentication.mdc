---
description: 用户认证相关方法
globs: 
alwaysApply: false
---
# 用户认证相关方法

SamClub类提供了以下与用户认证相关的方法：

## 主要认证方法

- `send_code(string $phone, string $token, string $device_id, string $ip)` - 发送验证码到指定手机号
- `mobile(string $phone, string $code, string $device_id, string $ip)` - 使用手机号和验证码进行登录
- `check_login(string $token, string $device_id, string $ip)` - 检查用户登录状态

## 登录后的操作

登录成功后，系统会自动调用以下方法：

- `login_success(string $token, string $device_id, string $ip)` - 登录成功后的操作集合
- `user_select(string $token, string $device_id, string $ip)` - 用户选择
- `profile(string $token, string $device_id, string $ip)` - 获取用户资料
- `member_card_info(string $token, string $device_id, string $ip)` - 获取会员卡信息

## 加密与安全

认证过程中使用了以下加密方法：

- `header_encrypt(string $body, string $token, string $device_id, string $device_type, string $lon, string $lat)` - 生成加密的请求头
- `AES_Encrypt(string $data, string $key, string $iv)` - AES加密方法
