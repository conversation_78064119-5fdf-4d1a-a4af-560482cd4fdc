---
description: HTTP请求处理
globs: 
alwaysApply: false
---
# HTTP请求处理

项目中的HTTP请求处理主要通过以下方式实现：

## SDK_Http类

[SDK_Http.php](mdc:SDK_Http.php)类提供了HTTP请求的基础功能：

- `http_curl($url, $paras = array())` - 核心HTTP请求方法，支持各种HTTP请求选项

## 请求参数

http_curl方法支持的参数包括：

- `method` - 请求方法（GET, POST, PUT等）
- `Header` - 自定义HTTP头
- `post` - POST请求数据
- `json` - JSON格式的请求数据
- `files` - 上传文件
- `proxy` - 代理设置
- `ip` - IP设置
- `cookie` - Cookie设置
- `cookieFile` - Cookie文件
- `auto` - 自动重定向
- `refer` - 引用页
- `ua` - User-Agent
- 等等

## SamClub类中的请求方法

SamClub类中封装了与山姆会员商店API交互的HTTP请求：

- `http_curl($url, $options = [])` - 调用SDK_Http的http_curl方法
- `request(string $url, string $post_data, string $token, string $device_id, string $ip)` - 统一请求方法
- `header_encrypt(string $body, string $token, string $device_id, string $device_type, string $lon, string $lat)` - 生成加密的请求头

## 请求安全

请求过程中使用了多种安全措施：

- 请求头加密
- AES数据加密
- 签名生成与验证
