<?php

require_once 'SamClub.php';
require_once 'SamClubOrder.php';

/**
 * 山姆会员商店订单处理示例
 */
class SamClubOrderExample
{
    /**
     * 运行订单处理示例
     */
    public function run()
    {
        $tokens = '740d926b981716f4de1df2abd66b2ef69632c5704f9f9f035fc8cc2b6666f468d8b5da3be7f955858f9d1a3f88fb7dfad76a9fbe76cd0b92';

        // 分割tokens
        $tokens = explode("\n", $tokens);
        foreach ($tokens as $token){
            $token = trim($token);
            // 用户认证令牌，实际使用时需要通过登录接口获取
            //$token = '740d926b981716f478f822e6026cbbca989bb5f058f266f354f38ca53b23df6d608ab68c3475eaf58f9d1a3f88fb7dfa11d161f99b431d2c';
            $deviceId = '';
            $ip = ''; // 可选的代理IP

            // 创建订单处理实例
            $orderProcessor = new SamClubOrder($token, $deviceId, $ip);

            // 准备订单数据
            $orderData = $this->prepareOrderData();

            // 处理订单
            $result = $orderProcessor->processOrder($orderData);

            // 输出处理结果
            $this->displayResult($result);
        }


    }
    
    /**
     * 准备订单数据
     * 
     * @return array 订单数据
     */
    private function prepareOrderData(): array
    {
        return [
            "receiverInfo" => [
                "phone" => "18888888888",
                "backupPhone" => "18888888888",
                "address" => "广东省/广州市 似酒likejuju(天河六运店)",
                "doorNumber" => "11",
                "latitude" => "23.130506",
                "longitude" => "113.325597"
            ],
            "deliveryInfo" => [
                "specificLocation" => "不指定",
                "deliveryType" => "极速达",
                "deliveryTime" => "2025/07/12 周六 21:30-22:30",
                "deliveryTimeData" => [
                    "displayTime" => "2025/07/12 周六 21:30-22:30",
                    "dateInfo" => "2025/07/12 周六",
                    "startTime" => "21:30",
                    "endTime" => "22:30",
                    "startRealTime" => "1752327000000",
                    "endRealTime" => "1752330600000",
                    "fullData" => [
                        "date" => "2025/07/12 周六",
                        "startTime" => "21:30",
                        "endTime" => "22:30",
                        "startRealTime" => "1752327000000",
                        "endRealTime" => "1752330600000",
                        "closeDate" => "2025-07-12",
                        "closeTime" => "21:20"
                    ]
                ]
            ],
            "orderMeta" => [
                "remark" => "",
                "platformOrderId" => "",
                "tearReceipt" => true
            ],
            "items" => [
                [
                    "spuId" => "206347770",
                    "storeId" => "5260",
                    "goodsName" => "MM 冰淇淋风味泡芙 16个",
                    "price" => 7990,
                    "quantity" => 1,
                    "totalPrice" => 7990,
                    "image" => "https://sam-material-online-1302115363.file.myqcloud.com//sams-static/goods/353595/bktpromotion-e2e-prod-8536982601202364416.jpg",
                    "deliveryType" => "极速达",
                    "originalDeliveryType" => "极速达",
                    "originalStoreId" => "5260",
                    "selected" => true,
                    "stock" => 16,
                    "limitInfo" => [

                    ]
                ],
                [
                    "spuId" => "328191649",
                    "storeId" => "5260",
                    "goodsName" => "PANPAN法式小泡芙 （核桃香草味）560g（16包）",
                    "price" => 4990,
                    "quantity" => 1,
                    "totalPrice" => 4990,
                    "image" => "https://sam-material-online-1302115363.file.myqcloud.com//sams-static/goods/465124/bktsitem-ops-prod-8631903908326715393.jpg",
                    "deliveryType" => "极速达",
                    "originalDeliveryType" => "极速达",
                    "originalStoreId" => "5260",
                    "selected" => true,
                    "stock" => 43,
                    "limitInfo" => [

                    ]
                ]
            ],
            "amount" => [
                "packagingFee" => 200,
                "serviceFee" => 500,
                "productAmount" => 12980,
                "deliveryFee" => 0,
                "totalAmount" => 13680
            ],
            "paymentMethod" => "alipay",
            "storeList" => [
                [
                    "storeId" => "6758",
                    "storeName" => "深圳山姆电商拣货中心",
                    "storeType" => "256",
                    "address" => "",
                    "distance" => "",
                    "areaBlockId" => "42305",
                    "storeDeliveryTemplateId" => "1845618165690835734",
                    "deliveryModeId" => "1013",
                    "allDeliveryAttrList" => [
                        "9",
                        "13"
                    ]
                ],
                [
                    "storeId" => "6518",
                    "storeName" => "广州天河山姆会员商店",
                    "storeType" => "2",
                    "address" => "",
                    "distance" => "",
                    "areaBlockId" => "292366873717286934",
                    "storeDeliveryTemplateId" => "796945157814677270",
                    "deliveryModeId" => "1004",
                    "allDeliveryAttrList" => [
                        "3",
                        "4",
                        "6",
                        "14",
                        "12",
                        "5",
                        "2",
                        "7",
                        "13"
                    ]
                ],
                [
                    "storeId" => "5260",
                    "storeName" => "广州体东DC",
                    "storeType" => "4",
                    "address" => "",
                    "distance" => "",
                    "areaBlockId" => "1923529524218348054",
                    "storeDeliveryTemplateId" => "2011594236671199510",
                    "deliveryModeId" => "1009",
                    "allDeliveryAttrList" => [
                        "3",
                        "4"
                    ]
                ],
                [
                    "storeId" => "9992",
                    "storeName" => "山姆全球购保税平潭仓",
                    "storeType" => "8",
                    "address" => "",
                    "distance" => "",
                    "areaBlockId" => "42305",
                    "storeDeliveryTemplateId" => "1743371277198079766",
                    "deliveryModeId" => "1010",
                    "allDeliveryAttrList" => [
                        "1"
                    ]
                ]
            ]
        ];
    }
    
    /**
     * 显示处理结果
     * 
     * @param array $result 处理结果
     */
    private function displayResult(array $result)
    {
        echo "订单处理结果：" . ($result['success'] ? '成功' : '失败') . "\n";
        
        if (!$result['success']) {
            echo "错误信息：" . $result['error'] . "\n";
        } else {
            echo "订单ID：" . ($result['orderId'] ?? '未获取到订单ID') . "\n";
        }
        
        echo "\n处理步骤详情：\n";
        foreach ($result['steps'] as $step => $stepResult) {
            echo "- {$step}: " . ($stepResult['success'] ? '成功' : '失败');
            if (!$stepResult['success']) {
                echo " ({$stepResult['message']})";
            }
            echo "\n";
        }
    }
}

// 运行示例
$example = new SamClubOrderExample();
$example->run(); 