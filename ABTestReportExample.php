<?php
/**
 * AB測試報告使用示例
 * 
 * 本文件展示了如何使用SamClub類中的abtest_report方法
 * 用於向山姆會員商店報告AB測試數據，進行風控驗證
 */

require_once 'SamClub.php';

class ABTestReportExample
{
    private $samClub;
    
    public function __construct()
    {
        $this->samClub = new \app\common\model\SamClub();
    }
    
    /**
     * 運行AB測試報告示例
     */
    public function run()
    {
        echo "=== 山姆會員商店 AB測試報告示例 ===\n\n";
        
        // 用戶認證信息
        $token = '740d926b981716f453c99383bc8d86d84c546b2a0de1094e546c4e3745c107d21dd523b87df46a648f9d1a3f88fb7dfae5e72da7e22d4d58';
        $device_id = 'c35157ba84861d22b1f185f77b578ef3';
        $ip = ''; // 可選的代理IP
        
        echo "1. 使用默認參數發送AB測試報告...\n";
        $result1 = $this->samClub->abtest_report($token, $device_id, $ip);
        echo "結果: " . $result1 . "\n\n";
        
        echo "2. 使用自定義參數發送AB測試報告...\n";
        $custom_options = [
            'businessCode' => '9191',
            'expId' => '795',
            'expKey' => 'exp_custom_test',
            'groupKey' => 'exp_custom_group',
            'kaName' => 'SAMS',
            'layerKey' => 'exp_custom_layer',
            'params' => [
                'testParam' => 'testValue',
                'version' => '1.0.0'
            ],
            'qimei' => 'custom_device_fingerprint',
            'reportPath' => 'SAMS_custom_test_report',
            'userType' => 1,
            'timestamp' => time() * 1000 // 當前時間戳（毫秒）
        ];
        
        $result2 = $this->samClub->abtest_report($token, $device_id, $ip, $custom_options);
        echo "結果: " . $result2 . "\n\n";
        
        echo "3. 批量發送多個AB測試報告...\n";
        $this->sendMultipleReports($token, $device_id, $ip);
        
        echo "=== 示例完成 ===\n";
    }
    
    /**
     * 批量發送多個AB測試報告
     */
    private function sendMultipleReports($token, $device_id, $ip)
    {
        $test_cases = [
            [
                'name' => '3D觸控測試',
                'options' => [
                    'businessCode' => '9191',
                    'expId' => '794',
                    'expKey' => 'exp_3dtouch_C',
                    'groupKey' => 'exp_3dtouch',
                    'reportPath' => 'SAMS_online_widget3DTouchExp_exp_3dtouch'
                ]
            ],
            [
                'name' => '界面優化測試',
                'options' => [
                    'businessCode' => '9191',
                    'expId' => '796',
                    'expKey' => 'exp_ui_optimization',
                    'groupKey' => 'exp_ui_group',
                    'reportPath' => 'SAMS_ui_optimization_test'
                ]
            ],
            [
                'name' => '性能監控測試',
                'options' => [
                    'businessCode' => '9191',
                    'expId' => '797',
                    'expKey' => 'exp_performance_monitor',
                    'groupKey' => 'exp_performance_group',
                    'reportPath' => 'SAMS_performance_monitor_test'
                ]
            ]
        ];
        
        foreach ($test_cases as $test_case) {
            echo "發送 {$test_case['name']} 報告...\n";
            $result = $this->samClub->abtest_report($token, $device_id, $ip, $test_case['options']);
            
            // 解析結果
            $response = json_decode($result, true);
            if ($response && isset($response['success']) && $response['success']) {
                echo "✓ {$test_case['name']} 報告發送成功\n";
            } else {
                echo "✗ {$test_case['name']} 報告發送失敗: " . $result . "\n";
            }
        }
        echo "\n";
    }
    
    /**
     * 解析AB測試報告響應
     */
    public function parseResponse($response)
    {
        $data = json_decode($response, true);
        
        if (!$data) {
            return "響應解析失敗";
        }
        
        $result = [
            'success' => $data['success'] ?? false,
            'code' => $data['code'] ?? '',
            'message' => $data['msg'] ?? '',
            'error_message' => $data['errorMsg'] ?? '',
            'trace_id' => $data['traceId'] ?? '',
            'request_id' => $data['requestId'] ?? '',
            'response_time' => $data['rt'] ?? 0,
            'client_ip' => $data['clientIp'] ?? null
        ];
        
        return $result;
    }
}

// 運行示例
if (php_sapi_name() === 'cli') {
    $example = new ABTestReportExample();
    $example->run();
} else {
    echo "此示例需要在命令行環境中運行";
} 