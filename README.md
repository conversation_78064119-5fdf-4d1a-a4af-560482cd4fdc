# 山姆会员商店API接口

这个项目提供了与山姆会员商店API交互的PHP接口，可以实现用户认证、商品搜索、购物车管理、订单处理等功能。

## 项目结构

- `SamClub.php` - 主类文件，包含与山姆会员商店API交互的所有方法
- `SDK_Http.php` - HTTP请求处理类，处理所有的API请求
- `SamClubOrder.php` - 订单处理类，封装了完整的订单流程
- `SamClubOrderExample.php` - 使用示例
- `public/sams_key/` - 存储API密钥的目录
- `数据包/` - 包含各种API响应示例的目录，用于参考和测试

## SamClubOrder 类

`SamClubOrder` 类封装了完整的订单流程，包括以下步骤：

1. 添加收货地址
2. 清空购物车
3. 添加商品到购物车
4. 获取购物车数据
5. 获取报价单
6. 获取结算单
7. 提交支付

### 使用方法

```php
// 创建SamClubOrder实例
$orderProcessor = new SamClubOrder($token, $deviceId, $ip);

// 准备订单数据
$orderData = [
    "uid" => "1818149219057", // 用户唯一标识
    "receiverInfo" => [
        "phone" => "18888888888",
        "address" => "广东省/惠州市 天益城购物中心",
        "doorNumber" => "天益城购物中心",
        "latitude" => "23.005154",
        "longitude" => "114.327424"
    ],
    "deliveryInfo" => [
        // 配送信息...
    ],
    "orderMeta" => [
        "remark" => "",
        "tearReceipt" => true
    ],
    "items" => [
        // 商品列表...
    ],
    "amount" => [
        // 金额信息...
    ],
    "paymentMethod" => "alipay",
    "storeList" => [
        // 门店列表...
    ]
];

// 处理订单
$result = $orderProcessor->processOrder($orderData);
```

### 订单数据格式

订单数据需要包含以下字段：

- `uid` - 用户唯一标识
- `receiverInfo` - 收货人信息
  - `phone` - 手机号
  - `address` - 地址（格式：省份/城市 详细地址）
  - `doorNumber` - 门牌号
  - `latitude` - 纬度
  - `longitude` - 经度

- `deliveryInfo` - 配送信息
  - `deliveryType` - 配送类型
  - `deliveryTime` - 配送时间
  - `deliveryTimeData` - 配送时间详细数据
    - `displayTime` - 显示时间
    - `dateInfo` - 日期信息
    - `startTime` - 开始时间
    - `endTime` - 结束时间
    - `startRealTime` - 开始时间戳
    - `endRealTime` - 结束时间戳
    - `fullData` - 完整数据
      - `date` - 日期
      - `startTime` - 开始时间
      - `endTime` - 结束时间
      - `closeDate` - 截止日期
      - `closeTime` - 截止时间

- `orderMeta` - 订单元数据
  - `remark` - 备注
  - `tearReceipt` - 是否需要小票

- `items` - 商品列表
  - `spuId` - 商品ID
  - `storeId` - 门店ID
  - `quantity` - 数量
  - `price` - 价格
  - `goodsName` - 商品名称
  - `image` - 商品图片

- `amount` - 金额信息
  - `packagingFee` - 包装费
  - `serviceFee` - 服务费
  - `productAmount` - 商品总额
  - `deliveryFee` - 配送费
  - `totalAmount` - 总金额

- `paymentMethod` - 支付方式
- `storeList` - 门店列表
  - `storeId` - 门店ID
  - `storeName` - 门店名称
  - `storeType` - 门店类型
  - `areaBlockId` - 区域ID
  - `storeDeliveryTemplateId` - 配送模板ID
  - `deliveryModeId` - 配送模式ID
  - `storeDeliveryAttr` - 门店配送属性

### 订单处理流程详解

#### 1. 添加收货地址
首先将用户提供的收货地址信息添加到系统中，获取地址ID用于后续流程。

#### 2. 清空购物车
清空用户当前购物车中的所有商品，准备添加新订单商品。

#### 3. 添加商品到购物车
将订单中的商品逐一添加到购物车中。

#### 4. 获取购物车数据
获取更新后的购物车数据，包括商品信息、价格、库存等。

#### 5. 获取报价单
根据购物车数据获取报价单，包括商品价格、配送费、包装费等信息。报价单数据结构为：
```json
{
  "uid": "用户ID",
  "storeList": [
    {
      "storeId": "门店ID",
      "storeType": "门店类型",
      "areaBlockId": "区域ID",
      "storeDeliveryTemplateId": "配送模板ID",
      "deliveryModeId": "配送模式ID",
      "storeDeliveryAttr": [配送属性数组]
    }
  ],
  "checkValidGoodsVOList": [
    {
      "floorId": "楼层ID",
      "giveawayList": [],
      "goodsList": [
        {
          "spuId": "商品ID",
          "storeId": "门店ID",
          "quantity": 数量,
          "isSelected": true,
          "warrantyExtensionSpuIdList": []
        }
      ]
    }
  ],
  "addressId": "地址ID"
}
```

#### 6. 获取结算单
根据报价单数据获取结算单，包括最终价格、配送信息、支付信息等。结算单数据结构为：
```json
{
  "uid": "用户ID",
  "addressId": "地址ID",
  "settleFloorDataList": [
    {
      "floorId": "楼层ID",
      "storeInfo": {
        "storeId": "门店ID",
        "storeType": "门店类型",
        "areaBlockId": "区域ID"
      },
      "deliveryType": 配送类型,
      "cartDeliveryType": 0,
      "hkDeliveryMode": 0,
      "couponList": [],
      "deliveryInfoVO": {
        "deliveryModeId": "配送模式ID",
        "storeDeliveryTemplateId": "配送模板ID",
        "storeType": "门店类型"
      },
      "goodsList": [
        {
          "spuId": "商品ID",
          "storeId": "门店ID",
          "quantity": 数量,
          "isSelected": true,
          "brandId": "品牌ID",
          "categoryIds": ["分类ID"],
          "componentPath": "",
          "deliveryAttr": 3,
          "goodName": "商品名称",
          "goodsWight": 0,
          "isRoutine": true,
          "price": 价格,
          "skuId": 0,
          "ticketId": "",
          "warrantyExtensionSpuIdList": []
        }
      ],
      "openCollectOrder": false,
      "isSelfPickup": false
    }
  ],
  "gray": true,
  "showBuyToGo": true,
  "buyToGoChangeAddress": false
}
```

#### 7. 提交支付
最后根据结算单数据提交支付，完成订单流程。

## 示例

查看 `SamClubOrderExample.php` 文件获取完整的使用示例。

## AB測試報告功能

### abtest_report 方法

該方法用於向山姆會員商店報告AB測試數據，進行風控驗證和用戶行為分析。

#### 使用方法

```php
// 創建SamClub實例
$samClub = new SamClub();

// 基本使用（使用默認參數）
$result = $samClub->abtest_report($token, $device_id, $ip);

// 自定義參數使用
$options = [
    'businessCode' => '9191',
    'expId' => '795',
    'expKey' => 'exp_custom_test',
    'groupKey' => 'exp_custom_group',
    'kaName' => 'SAMS',
    'layerKey' => 'exp_custom_layer',
    'params' => [
        'testParam' => 'testValue',
        'version' => '1.0.0'
    ],
    'qimei' => 'custom_device_fingerprint',
    'reportPath' => 'SAMS_custom_test_report',
    'userType' => 1,
    'timestamp' => time() * 1000
];

$result = $samClub->abtest_report($token, $device_id, $ip, $options);
```

#### 參數說明

- `$token` (string) - 用戶令牌（必填）
- `$device_id` (string) - 設備ID（可選）
- `$ip` (string) - 代理IP（可選）
- `$options` (array) - 可選參數
  - `businessCode` - 業務代碼，默認為"9191"
  - `expId` - 實驗ID，默認為"794"
  - `expKey` - 實驗鍵，默認為"exp_3dtouch_C"
  - `groupKey` - 分組鍵，默認為"exp_3dtouch"
  - `kaName` - 應用名稱，默認為"SAMS"
  - `layerKey` - 層級鍵，默認為"exp_3dtouch"
  - `params` - 參數對象，默認為空對象{}
  - `qimei` - 設備指紋，默認為空字符串
  - `reportPath` - 報告路徑，默認為"SAMS_online_widget3DTouchExp_exp_3dtouch"
  - `userType` - 用戶類型，默認為2
  - `timestamp` - 時間戳，默認為當前時間戳

#### 響應格式

```json
{
  "data": "success",
  "code": "Success",
  "msg": "",
  "errorMsg": "",
  "traceId": "86f3557fd270834c",
  "requestId": "f03c8a5c770c492c9b8c48b1a3e21564.421.17552496742405327",
  "rt": 0,
  "clientIp": null,
  "success": true
}
```

#### 使用示例

查看 `ABTestReportExample.php` 文件獲取完整的使用示例。

## 注意事项

- 使用前需要先获取有效的用户令牌（token）
- 所有接口都需要正确的认证信息
- 部分接口可能需要设置代理IP
- 请确保网络环境稳定，以避免订单处理中断
- 订单处理过程中的数据结构需要严格按照API要求进行格式化
- AB測試報告方法主要用於風控驗證，建議定期調用以維持賬戶活躍度 