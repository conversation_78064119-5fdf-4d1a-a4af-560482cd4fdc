<?php

/**
 * 山姆会员商店订单处理类
 * 
 * 该类用于处理山姆会员商店的订单流程，包括添加收货地址、清空购物车、获取购物车商品、
 * 获取报价单、获取结算单、提交支付等功能。
 */
class SamClubOrder
{
    /**
     * SamClub SDK实例
     * @var SamClub
     */
    private $samClub;
    
    /**
     * 用户认证令牌
     * @var string
     */
    private $token;
    
    /**
     * 设备ID
     * @var string
     */
    private $deviceId;
    
    /**
     * 代理IP
     * @var string
     */
    private $ip;
    
    /**
     * 构造函数
     * 
     * @param string $token 用户认证令牌
     * @param string $deviceId 设备ID
     * @param string $ip 代理IP
     */
    public function __construct(string $token, string $deviceId = '', string $ip = '')
    {
        $this->samClub = new app\common\model\SamClub();
        $this->token = $token;
        $this->deviceId = $deviceId;
        $this->ip = $ip;
    }
    
    /**
     * 处理订单流程
     * 
     * 完整的订单处理流程，包括添加收货地址、清空购物车、添加商品到购物车、获取报价单、获取结算单、提交支付
     * 
     * @param array $orderData 订单数据
     * @return array 处理结果，包含每个步骤的状态和响应数据
     */
    public function processOrder(array $orderData): array
    {
        $result = [
            'success' => true,
            'steps' => [],
            'error' => null
        ];
        
        try {

            //  删除全部收货地址
            $addressResponse = $this->samClub->get_address_list($this->token, $this->deviceId, $this->ip);
            $addressData = json_decode($addressResponse, true);
            if ($addressData && isset($addressData['success']) && $addressData['success'] && !empty($addressData['data']['addressList'])) {
                foreach ($addressData['data']['addressList'] as $address) {
                    $this->samClub->delete_receiver_address($address['addressId'], $this->token, $this->deviceId, $this->ip);
                }
            }

            // 1. 添加收货地址
            $addressResult = $this->addAddress($orderData);
            $result['steps']['address'] = $addressResult;
            
            if (!$addressResult['success']) {
                throw new Exception("添加收货地址失败: " . $addressResult['message']);
            }
            
            // 将地址ID添加到订单数据中，供后续步骤使用
            $orderData['addressId'] = $addressResult['addressId'];
            
            // 2. 清空购物车
            $clearCartResult = $this->clearCart();
            $result['steps']['clearCart'] = $clearCartResult;
            
            if (!$clearCartResult['success']) {
                throw new Exception("清空购物车失败: " . $clearCartResult['message']);
            }

            // 3. 添加商品到购物车
            $addToCartResult = $this->addItemsToCart($orderData['items']);
            $result['steps']['addToCart'] = $addToCartResult;
            
            if (!$addToCartResult['success']) {
                throw new Exception("添加商品到购物车失败: " . $addToCartResult['message']);
            }
            
            // 4. 获取购物车数据
            $cartResult = $this->getCart($orderData['storeList']);
            $result['steps']['cart'] = $cartResult;
            
            if (!$cartResult['success']) {
                throw new Exception("获取购物车数据失败: " . $cartResult['message']);
            }
            
            // 5. 获取报价单
            $preSettleResult = $this->getPreSettleInfo($orderData, $cartResult['data']);
            $result['steps']['preSettle'] = $preSettleResult;
            
            if (!$preSettleResult['success']) {
                throw new Exception("获取报价单失败: " . $preSettleResult['message']);
            }
            
            // 6. 获取结算单
            $settleResult = $this->getSettleInfo($orderData, $preSettleResult['data']);
            $result['steps']['settle'] = $settleResult;
            
            if (!$settleResult['success']) {
                throw new Exception("获取结算单失败: " . $settleResult['message']);
            }

            
            // 获取支付方式
            $paymentResult = $this->getPaymentMethods($orderData, $settleResult['data']);
            if (!$paymentResult['success']) {
                throw new Exception("获取支付方式失败: " . $paymentResult['message']);
            }

            // 取出对应的  paymentMethod 中的 payMethodId
            foreach ($paymentResult['data']['paymentMethods'] as $payment) {

                if ($payment['channel'] == $orderData['paymentMethod']) {
                    $orderData['payMethodId'] = $payment['subSaasId'];
                    break;
                }
            }
            // 7. 提交支付
            $payResult = $this->commitPay($orderData, $settleResult['data']);
            $result['steps']['pay'] = $payResult;

            if (!$payResult['success']) {
                throw new Exception("提交支付失败: " . $payResult['message']);
            }

            $result['orderId'] = $payResult['data']['orderNo'] ?? null;
            
        } catch (Exception $e) {
            $result['success'] = false;
            $result['error'] = $e->getMessage();
        }
        
        return $result;
    }
    
    /**
     * 添加收货地址
     * 
     * @param array $orderData 订单数据
     * @return array 处理结果
     */
    private function addAddress(array $orderData): array
    {
        $receiverInfo = $orderData['receiverInfo'];
        
        // 解析地址信息
        $addressParts = explode('/', $receiverInfo['address']);
        $provinceName = $addressParts[0] ?? '';
        $cityDistrict = $addressParts[1] ?? '';
        
        // 进一步解析城市和区域
        $cityDistrictParts = explode(' ', $cityDistrict);
        $cityName = $cityDistrictParts[0] ?? '';
        $districtName = $cityDistrictParts[1] ?? '';
        
        $addressOptions = [
            'mobile' => $receiverInfo['phone'],
            'name' => '收货人', // 默认名称，实际应从订单数据中获取
            'countryCode' => '+86',
            'streetName' => '',
            'isDefault' => 2,
            'receiverAddress' => $receiverInfo['address'],
            'cityName' => $cityName,
            'detailAddress' => $receiverInfo['doorNumber'],
            'districtName' => $districtName,
            'latitude' => $receiverInfo['latitude'],
            'longitude' => $receiverInfo['longitude'],
            'provinceName' => $provinceName,
            'addressTag' => '',
            'mapAddress' => $receiverInfo['address']
        ];
        
        try {
            $response = $this->samClub->add_receiver_address($this->token, $this->deviceId, $this->ip, $addressOptions);
            $responseData = json_decode($response, true);
            
            if ($responseData && isset($responseData['success']) && $responseData['success']) {
                $addressId = $responseData['data']['addressId'] ?? 0;
                return [
                    'success' => true,
                    'addressId' => $addressId,
                    'data' => $responseData
                ];
            } else {
                return [
                    'success' => false,
                    'message' => $responseData['msg'] ?? '添加地址失败',
                    'data' => $responseData
                ];
            }
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage(),
                'data' => null
            ];
        }
    }
    
    /**
     * 清空购物车
     * 
     * @return array 处理结果
     */
    private function clearCart(): array
    {
        try {
            // 1. 获取购物车数据
            $cartResponse = $this->samClub->get_cart($this->token, $this->deviceId, $this->ip);
            $cartData = json_decode($cartResponse, true);
            
            if (!$cartData || !isset($cartData['success']) || !$cartData['success']) {
                return [
                    'success' => false,
                    'message' => $cartData['msg'] ?? '获取购物车数据失败',
                    'data' => $cartData
                ];
            }
            
            // 2. 如果购物车为空，直接返回成功
            if (empty($cartData['data']['floorInfoList'])) {
                return [
                    'success' => true,
                    'message' => '购物车已为空',
                    'data' => $cartData
                ];
            }
            
            // 3. 准备要删除的商品列表
            $cartGoodsList = [];
            foreach ($cartData['data']['floorInfoList'] as $floor) {
                if (isset($floor['normalGoodsList'])) {
                    foreach ($floor['normalGoodsList'] as $goods) {
                        $cartGoodsList[] = [
                            'spuId' => $goods['spuId'],
                            'storeId' => $goods['storeId']
                        ];
                    }
                }
            }
            
            if (empty($cartGoodsList)) {
                return [
                    'success' => true,
                    'message' => '购物车已为空',
                    'data' => $cartData
                ];
            }
            
            // 4. 删除购物车商品
            $deleteOptions = [
                'cartGoodsList' => $cartGoodsList
            ];
            
            $deleteResponse = $this->samClub->delete_cart_goods($this->token, $this->deviceId, $this->ip, $deleteOptions);
            $deleteData = json_decode($deleteResponse, true);
            
            if ($deleteData && isset($deleteData['success']) && $deleteData['success']) {
                return [
                    'success' => true,
                    'message' => '购物车清空成功',
                    'data' => $deleteData
                ];
            } else {
                return [
                    'success' => false,
                    'message' => $deleteData['msg'] ?? '清空购物车失败',
                    'data' => $deleteData
                ];
            }
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage(),
                'data' => null
            ];
        }
    }
    
    /**
     * 添加商品到购物车
     * 
     * @param array $items 商品列表
     * @return array 处理结果
     */
    private function addItemsToCart(array $items): array
    {
        $results = [
            'success' => true,
            'items' => [],
            'data' => null
        ];
        
        try {
            foreach ($items as $item) {
                $spuId = (int)$item['spuId'];
                $storeId = (int)$item['storeId'];
                $quantity = (int)$item['quantity'];
                
                $options = [
                    'quantity' => $quantity,
                    'isSelected' => true
                ];
                
                $response = $this->samClub->add_to_cart($spuId, $storeId, $this->token, $this->deviceId, $this->ip, $options);
                $responseData = json_decode($response, true);
                
                $itemResult = [
                    'spuId' => $spuId,
                    'storeId' => $storeId,
                    'quantity' => $quantity,
                    'success' => false,
                    'message' => '',
                    'data' => $responseData
                ];
                
                if ($responseData && isset($responseData['success']) && $responseData['success']) {
                    $itemResult['success'] = true;
                    $itemResult['message'] = '添加成功';
                } else {
                    $itemResult['message'] = $responseData['msg'] ?? '添加失败';
                    $results['success'] = false;
                }
                
                $results['items'][] = $itemResult;
            }
            
            return $results;
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage(),
                'items' => [],
                'data' => null
            ];
        }
    }
    
    /**
     * 获取购物车数据
     * 
     * @param array $storeList 门店列表
     * @return array 处理结果
     */
    private function getCart(array $storeList): array
    {
        try {
            // 准备门店信息
            $formattedStoreList = [];
            foreach ($storeList as $store) {
                $formattedStoreList[] = [
                    'storeId' => (int)$store['storeId'],
                    'storeType' => (int)$store['storeType'],
                    'areaBlockId' => $store['areaBlockId'],
                    'storeDeliveryTemplateId' => $store['storeDeliveryTemplateId'],
                    'deliveryModeId' => $store['deliveryModeId']
                ];
            }
            
            $options = [
                'storeList' => $formattedStoreList
            ];
            
            $response = $this->samClub->get_cart($this->token, $this->deviceId, $this->ip, $options);
            $responseData = json_decode($response, true);
            
            if ($responseData && isset($responseData['success']) && $responseData['success']) {
                return [
                    'success' => true,
                    'message' => '获取购物车数据成功',
                    'data' => $responseData['data']
                ];
            } else {
                return [
                    'success' => false,
                    'message' => $responseData['msg'] ?? '获取购物车数据失败',
                    'data' => $responseData
                ];
            }
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage(),
                'data' => null
            ];
        }
    }
    
    /**
     * 获取报价单
     * 
     * @param array $orderData 订单数据
     * @param array $cartData 购物车数据
     * @return array 处理结果
     */
    private function getPreSettleInfo(array $orderData, array $cartData): array
    {
        try {
            $deliveryType = $orderData['deliveryInfo']['deliveryType'];

            // 准备门店列表
            $storeList = [];
            foreach ($orderData['storeList'] as $store) {
                $storeList[] = [
                    'storeId' => (int)$store['storeId'],
                    'storeType' => (int)$store['storeType'],
                    'areaBlockId' => $store['areaBlockId'],
                    'storeDeliveryTemplateId' => $store['storeDeliveryTemplateId'],
                    'deliveryModeId' => $store['deliveryModeId'],
                    'storeDeliveryAttr' => isset($store['storeDeliveryAttr']) ? array_map('intval', $store['storeDeliveryAttr']) : [],
                    'storeDeliveryTemplateIdOriginal' => -1
                ];
            }
            
            // 准备商品列表
            $checkValidGoodsVOList = [];
            foreach ($cartData['floorInfoList'] as $floor) {
                $floorId = $deliveryType == '极速达' ? 104 : ($deliveryType == '全城配' ? 105 : ($deliveryType == '全球购' ? 2 : $floor['floorId']));
                if (isset($floor['normalGoodsList'])) {
                    $floorGoods = [
                        'floorId' => $floorId,
                        'giveawayList' => [],
                        'goodsList' => []
                    ];
                    
                    foreach ($floor['normalGoodsList'] as $goods) {
                        if ($goods['isSelected']) {
                            $storeId = ($match = array_values(array_filter($orderData['items'], function ($item) use ($goods) {
                                return $item['spuId'] == $goods['spuId'];
                            }))) ? $match[0]['storeId'] : $goods['storeId'];

                            $floorGoods['goodsList'][] = [
                                'spuId' => $goods['spuId'],
                                'storeId' => $storeId,
                                'quantity' => $goods['quantity'],
                                'isSelected' => true,
                                'warrantyExtensionSpuIdList' => []
                            ];
                        }
                    }

                    
                    if (!empty($floorGoods['goodsList'])) {
                        $checkValidGoodsVOList[] = $floorGoods;
                    }
                }
            }
            
            // 获取地址ID - 优先使用orderData中的addressId
            $addressId = $orderData['addressId'] ?? null;
            if (!$addressId) {
                // 获取地址列表并使用第一个地址
                $addressResponse = $this->samClub->get_address_list($this->token, $this->deviceId, $this->ip);
                $addressData = json_decode($addressResponse, true);
                if ($addressData && isset($addressData['success']) && $addressData['success'] && !empty($addressData['data']['addressList'])) {
                    $addressId = $addressData['data']['addressList'][0]['addressId'];
                }
            }
            
            if (!$addressId) {
                return [
                    'success' => false,
                    'message' => '无法获取地址ID',
                    'data' => null
                ];
            }
            
            // 获取用户UID
            $uid = $orderData['uid'] ?? null;
            if (!$uid) {
                // 尝试从购物车数据中获取uid
                $uid = $cartData['uid'] ?? sprintf("%d", rand(1000000000000, 9999999999999));
            }
            
            $options = [
                'uid' => $uid,
                'storeList' => $storeList,
                'checkValidGoodsVOList' => $checkValidGoodsVOList,
                'addressId' => $addressId
            ];
            
            $response = $this->samClub->get_pre_settle_info($this->token, $this->deviceId, $this->ip, $options);
            $responseData = json_decode($response, true);
            
            if ($responseData && isset($responseData['success']) && $responseData['success']) {
                return [
                    'success' => true,
                    'message' => '获取报价单成功',
                    'data' => $responseData['data'],
                    'addressId' => $addressId
                ];
            } else {
                return [
                    'success' => false,
                    'message' => $responseData['msg'] ?? '获取报价单失败',
                    'data' => $responseData
                ];
            }
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage(),
                'data' => null
            ];
        }
    }
    
    /**
     * 获取结算单
     * 
     * @param array $orderData 订单数据
     * @param array $preSettleData 报价单数据
     * @return array 处理结果
     */
    private function getSettleInfo(array $orderData, array $preSettleData): array
    {
        try {
            // 准备结算楼层数据
            $settleFloorDataList = [];
            
            // 从报价单中提取结算楼层数据 - 使用preSettleInfoList而非floorInfoList
            if (isset($preSettleData['preSettleInfoList'])) {
                foreach ($preSettleData['preSettleInfoList'] as $floor) {
                    $floorData = [
                        'floorId' => $floor['floorId'],
                        'cartDeliveryType' => 1,
                        'hkDeliveryMode' => 0,
                        'couponList' => [],
                        'openCollectOrder' => $floor['openCollectOrder'] ?? false,
                        'isSelfPickup' => false
                    ];
                    
                    // 添加楼层名称
                    if (isset($floor['floorName'])) {
                        $floorData['floorName'] = $floor['floorName'];
                    }
                    
                    // 添加配送类型
                    // 由于preSettleData中可能没有直接的deliveryType字段，设置默认值
                    $floorData['deliveryType'] = 1; // 默认为极速达
                    
                    // 构建storeInfo
                    // 从validGoodsList的第一个商品中获取storeId和storeType
                    if (isset($floor['validGoodsList']) && !empty($floor['validGoodsList'])) {
                        $firstGoods = $floor['validGoodsList'][0];
                        $floorData['storeInfo'] = [
                            'storeId' => $firstGoods['storeId'],
                            'storeType' => $firstGoods['storeType'],
                            'areaBlockId' => $orderData['storeList'][0]['areaBlockId'] ?? ''
                        ];
                        
                        // 添加配送信息
                        $floorData['deliveryInfoVO'] = [
                            'deliveryModeId' => $orderData['storeList'][0]['deliveryModeId'] ?? 1009,
                            'storeDeliveryTemplateId' => $orderData['storeList'][0]['storeDeliveryTemplateId'] ?? '',
                            'storeType' => $firstGoods['storeType']
                        ];
                    }
                    
                    // 添加商品信息 - 从validGoodsList中获取
                    $goodsList = [];
                    if (isset($floor['validGoodsList'])) {
                        foreach ($floor['validGoodsList'] as $goods) {
                            if ($goods['isSelected']) {
                                $goodsList[] = [
                                    'spuId' => $goods['spuId'],
                                    'storeId' => $goods['storeId'],
                                    'quantity' => $goods['quantity'],
                                    'isSelected' => $goods['isSelected'],
                                    'brandId' => $goods['brandId'] ?? '',
                                    'categoryIds' => $goods['categoryIdList'] ?? [], // 注意字段名不同
                                    'componentPath' => $goods['componentPath'] ?? '',
                                    'deliveryAttr' => $goods['deliveryAttr'] ?? 3,
                                    'goodName' => $goods['goodsName'] ?? '',
                                    'goodsWight' => 0,
                                    'isRoutine' => $goods['isRoutine'] ?? true,
                                    'price' => $goods['price'] ?? 0,
                                    'skuId' => 0,
                                    'ticketId' => '',
                                    'warrantyExtensionSpuIdList' => []
                                ];
                            }
                        }
                    }
                    
                    $floorData['goodsList'] = $goodsList;
                    $settleFloorDataList[] = $floorData;
                }
            }
            
            // 获取用户UID
            $uid = $orderData['uid'] ?? $preSettleData['uid'] ?? sprintf("%d", rand(1000000000000, 9999999999999));
            
            // 确保使用正确的addressId
            $addressId = $orderData['addressId'] ?? $preSettleData['addressId'] ?? 0;
            
            // 如果仍然没有addressId，尝试从address列表中获取
            if (!$addressId && isset($preSettleData['isChooseAddress']) && $preSettleData['isChooseAddress']) {
                $addressResponse = $this->samClub->get_address_list($this->token, $this->deviceId, $this->ip);
                $addressData = json_decode($addressResponse, true);
                $addressId = ($addressData && isset($addressData['success']) && $addressData['success'] && !empty($addressData['data']['addressList'])) 
                    ? $addressData['data']['addressList'][0]['addressId'] 
                    : 0;
            }
            
            if (!$addressId) {
                return [
                    'success' => false,
                    'message' => '无法获取地址ID',
                    'data' => null
                ];
            }
            
            $options = [
                'uid' => $uid,
                'addressId' => $addressId,
                'settleFloorDataList' => $settleFloorDataList,
                'gray' => true,
                'showBuyToGo' => true,
                'buyToGoChangeAddress' => false
            ];
            
            $response = $this->samClub->get_settle_info($this->token, $this->deviceId, $this->ip, $options);
            $responseData = json_decode($response, true);
            
            return ($responseData && isset($responseData['success']) && $responseData['success']) 
                ? [
                    'success' => true,
                    'message' => '获取结算单成功',
                    'data' => $responseData['data']
                  ]
                : [
                    'success' => false,
                    'message' => $responseData['msg'] ?? '获取结算单失败',
                    'data' => $responseData
                  ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * 获取支付方式
     *
     * 该方法用于获取可用的支付方式列表，根据订单数据和结算单数据构建支付方式请求参数
     *
     * @param array $orderData 订单数据
     * @param array $settleData 结算单数据
     * @return array 处理结果
     */
    private function getPaymentMethods(array $orderData, array $settleData): array
    {
        try {
            // 构建支付方式请求列表
            $paymentList = [];
            // 从结算单数据中提取门店和商品信息
            if (isset($settleData['settleInfoList'])) {
                foreach ($settleData['settleInfoList'] as $floor) {
                    $storeId = $floor['storeId'];
                    $spuIdList = [];

                    // 提取选中商品的SPU ID列表
                    if (isset($floor['goodsInfo']['goodsList'])) {
                        foreach ($floor['goodsInfo']['goodsList'] as $goods) {
                            if ($goods['isSelected']) {
                                $spuIdList[] = $goods['spuId'];
                            }
                        }
                    }

                    // 如果有选中的商品，添加到支付方式请求列表
                    if (!empty($spuIdList)) {
                        // 根据门店类型确定订单类型
                        $orderType = $this->getOrderTypeByStoreType($floor['storeType']);

                        $paymentList[] = [
                            'clientType' => 'APP',
                            'orderType' => $orderType,
                            'spuIdList' => $spuIdList,
                            'storeId' => $storeId
                        ];
                    }
                }
            }

            // 如果没有从结算单中获取到数据，使用订单数据中的商品信息
            if (empty($paymentList) && isset($orderData['goods'])) {
                foreach ($orderData['goods'] as $goods) {
                    $paymentList[] = [
                        'clientType' => 'APP',
                        'orderType' => $goods['orderType'] ?? 'H', // 默认为H类型
                        'spuIdList' => [$goods['spuId']],
                        'storeId' => $goods['storeId']
                    ];
                }
            }

            // 构建请求选项
            $options = [
                'list' => $paymentList
            ];

            // 调用API获取支付方式
            $response = $this->samClub->get_payment_methods($this->token, $this->deviceId, $this->ip, $options);
            $responseData = json_decode($response, true);

            if ($responseData && isset($responseData['success']) && $responseData['success']) {

                return [
                    'success' => true,
                    'message' => '获取支付方式成功',
                    'data' => [
                        'paymentMethods' => $responseData['data']['paymentStoreInfoList'],
                    ]
                ];
            } else {
                return [
                    'success' => false,
                    'message' => $responseData['msg'] ?? '获取支付方式失败',
                    'data' => $responseData
                ];
            }

        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * 根据门店类型获取订单类型
     *
     * @param int $storeType 门店类型
     * @return string 订单类型
     */
    private function getOrderTypeByStoreType(int $storeType): string
    {
        // 根据门店类型映射订单类型
        switch ($storeType) {
            case 256: // 山姆会员商店
                return 'H'; // Home delivery 配送
            case 2:   // 云仓
                return 'A'; // 云仓订单
            case 4:   // 前置仓
                return 'H'; // 配送
            case 8:   // 其他类型
                return 'H'; // 默认配送
            default:
                return 'H'; // 默认配送
        }
    }

    /**
     * 提交支付
     *
     * @param array $orderData 订单数据
     * @param array $settleData 结算单数据
     * @return array 处理结果
     */
    private function commitPay(array $orderData, array $settleData): array
    {
        try {
            // 获取用户UID
            $uid = $orderData['uid'] ?? sprintf("%d", rand(1000000000000, 9999999999999));
            
            // 获取地址ID
            $addressId = $orderData['addressId'] ?? ($settleData['deliveryAddress']['addressId'] ?? null);
            
            if (!$addressId) {
                return [
                    'success' => false,
                    'message' => '无法获取地址ID',
                    'data' => null
                ];
            }
            
            // 准备结算信息列表
            $settleInfoList = [];
            
            if (isset($settleData['settleInfoList'])) {
                foreach ($settleData['settleInfoList'] as $floor) {
                    $floorId = $floor['floorId'];
                    $storeId = $floor['storeId'];
                    $storeType = $floor['storeType'];
                    $deliveryType = $floor['settleDelivery']['deliveryType'];
                    
                    // 构建商品列表
                    $goodsList = [];
                    if (isset($floor['goodsInfo']['goodsList'])) {
                        foreach ($floor['goodsInfo']['goodsList'] as $goods) {
                            if ($goods['isSelected']) {
                                $goodsList[] = [
                                    'skuId' => 0,
                                    'componentPath' => $goods['componentPath'] ?? '',
                                    'storeId' => $goods['storeId'],
                                    'goodName' => $goods['goodsName'],
                                    'spuId' => $goods['spuId'],
                                    'isSelected' => true,
                                    'labelList' => $goods['labelList'] ?? '',
                                    'quantity' => $goods['quantity'],
                                    'warrantyExtensionSpuIdList' => []
                                ];
                            }
                        }
                    }
                    
                    // 获取配送信息
                    $settleDelivery = $floor['settleDelivery'] ?? [];
                    $storeDeliveryTemplateId = $settleDelivery['storeDeliveryTemplateId'] ?? '';
                    $deliveryModeId = isset($settleDelivery['deliveryModeIdList'][0]) ? $settleDelivery['deliveryModeIdList'][0] : '';
                    $areaBlockId = $settleDelivery['areaBlockId'] ?? '';

                    // 构建storeInfo
                    foreach ($orderData['storeList'] as $store) {
                        if ($store['storeId'] == $storeId) {
                            $storeInfo = $store;
                            //$storeInfo['storeDeliveryAttr'] = $store['storeDeliveryAttr'];
                            break;
                        }
                    }
                    
                    // 构建settleDeliveryInfo
                    $settleDeliveryInfo = [
                        'changeStoreDeliveryTemplateId' => $storeDeliveryTemplateId,
                        'storeDeliveryTemplateId' => $storeDeliveryTemplateId,
                        'deliveryType' => $deliveryType
                    ];
                    
                    // 添加配送时间
                    if (isset($orderData['deliveryInfo']) && isset($orderData['deliveryInfo']['deliveryTimeData'])) {
                        $deliveryTimeData = $orderData['deliveryInfo']['deliveryTimeData'];
                        $fullData = $deliveryTimeData['fullData'];
                        
                        $settleDeliveryInfo['expectArrivalTime'] = $fullData['startRealTime'];
                        $settleDeliveryInfo['fastAccurateTag'] = true;
                        $settleDeliveryInfo['fastAccurateEndTime'] = $fullData['endRealTime'];
                        $settleDeliveryInfo['expectArrivalEndTime'] = $fullData['endRealTime'];
                    }
                    
                    // 获取缺货处理方式
                    $shortageId = 1; // 默认：其他商品继续配送（缺货商品直接退款）
                    $shortageDesc = '其他商品继续配送（缺货商品直接退款）';
                    if (isset($settleData['inCaseOfShortageList'])) {
                        foreach ($settleData['inCaseOfShortageList'] as $shortage) {
                            if ($shortage['isDefault']) {
                                $shortageId = $shortage['shortageId'];
                                $shortageDesc = $shortage['shortageDesc'];
                                break;
                            }
                        }
                    }
                    
                    // 构建结算信息
                    $settleInfo = [
                        'amount' => $floor['goodsInfo']['amount'] ?? '0',
                        'isSelfPickup' => false,
                        'openCollectOrder' => $floor['openCollectOrder'] ?? false,
                        'cartDeliveryType' => $deliveryType,
                        'couponList' => [],
                        'deliveryInfoVO' => [
                            'storeDeliveryTemplateId' => $storeDeliveryTemplateId,
                            'changeStoreDeliveryTemplateId' => $storeDeliveryTemplateId,
                            'deliveryModeId' => $deliveryModeId,
                            'storeType' => $storeType
                        ],
                        'promotionFee' => $floor['promotionFee'] ?? '0',
                        'floorId' => $floorId,
                        'goodsList' => $goodsList,
                        'orderType' => 0,
                        'settleDeliveryInfo' => $settleDeliveryInfo,
                        'changeStoreType' => 1,
                        'recommendRemark' => '',
                        'remark' => $orderData['orderMeta']['remark'] ?? '',
                        'shortageDesc' => $shortageDesc,
                        'shortageId' => $shortageId,
                        'storeInfo' => $storeInfo,
                        'putPlaceSwitch' => false,
                        'putPlaceDesc' => '',
                        'putPlaceDescEn' => '',
                        'loseContactAllowSelectPlace' => true
                    ];
                    
                    // 添加小票信息
                    if (isset($orderData['orderMeta']) && isset($orderData['orderMeta']['tearReceipt'])) {
                        $settleInfo['tearReceipt'] = $orderData['orderMeta']['tearReceipt'];
                    }
                    
                    $settleInfoList[] = $settleInfo;
                }
            }

            
            
            // 构建提交支付请求参数
            $options = [
                'uid' => $uid,
                'currency' => 'CNY',
                'settleInfoList' => $settleInfoList,
                'addressId' => $addressId,
                'channel' => $orderData['paymentMethod'] ?? 'wechat',
                'hkShopingProtocol' => true,
                'totalAmount' => $orderData['amount']['totalAmount'] ?? $settleData['totalAmountSum'] ?? 0,
                'payType' => 0,
                'payMethodId' => $orderData['payMethodId'] ?? '',
                'gray' => true,
                'isSelectShoppingNotes' => true,
                'useCash' => false,
                'cashInfo' => ['allCashTotal' => 0],
                'tongDunReportData' => $this->generateTongDunReportData($orderData)
            ];

            // 如果是微信支付 添加appid
            if ($options['channel'] == 'wechat') {
                $options['appId'] = 'wx57364320cb03dfba';
            }

            
            $response = $this->samClub->commit_pay($this->token, $this->deviceId, $this->ip, $options);
            echo $response;
            $responseData = json_decode($response, true);
            
            return ($responseData && isset($responseData['success']) && $responseData['success'])
                ? [
                    'success' => true,
                    'message' => '提交订单成功',
                    'data' => $responseData['data'],
                    'orderId' => $responseData['data']['orderId'] ?? null
                  ]
                : [
                    'success' => false,
                    'message' => $responseData['msg'] ?? '提交订单失败',
                    'data' => $responseData
                  ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage(),
                'data' => null
            ];
        }
    }
    
    /**
     * 生成同盾风控数据
     * 
     * 该方法用于生成订单提交时需要的同盾风控数据，用于风险控制和反欺诈
     * 包含了用户设备、位置和身份等相关信息，经过Base64编码后传递给服务端
     * 
     * @param array $orderData 订单数据
     * @return string Base64编码的同盾风控数据
     */
    private function generateTongDunReportData(array $orderData): string
    {

        // 获取位置信息
        $longitude = $orderData['receiverInfo']['longitude'] ?? 104.322471;
        $latitude = $orderData['receiverInfo']['latitude'] ?? 23.0257815;
        $province = $orderData['receiverInfo']['province'] ?? '广东';
        $city = $orderData['receiverInfo']['city'] ?? '深圳';
        $address = $orderData['receiverInfo']['address'] ?? ($province . $city);
        
        // 生成唯一标识符
        $uuid = uniqid(rand(10000, 99999), true);
        
        // 获取用户ID
        $userId = $orderData['userId'] ?? ('1818' . rand(100000000, 999999999));
        
        // 获取设备类型
        $appType = $orderData['deviceInfo']['appType'] ?? 'ios';
        $appChannel = $appType === 'ios' ? 'iOS' : 'Android';

        // 生成随机后缀
        $randomSuffix = substr(str_shuffle('abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'), 0, 10);

        // 获取或生成黑匣子数据（通常由同盾SDK生成，这里模拟）
        $blackbox = 'sMPVD17522040490d93mkzGj48';
        // 构建数据对象
        $data = [
            'userId' => $userId,
            'gpsAddr' => $address,
            'longitude' => $longitude,
            'appChannel' => $appChannel,
            'UUID' => $uuid,
            'latitude' => $latitude,
            'gpsProve' => $province,
            'localid' => '',
            'blackbox' => $blackbox,
            'appType' => $appType,
            'gpsCity' => $city
        ];

        $res = file_get_contents('http://**************:3000/risk-params?type=mini');
        //$res = file_get_contents('http://**************:3000/risk-params?type=web');
        $res = json_decode($res,true);
        return $res['data'];
        // 将数据转换为JSON并进行Base64编码
        return base64_encode(json_encode($data));
    }
} 